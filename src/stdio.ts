#!/usr/bin/env node
import { FastMCP } from "fastmcp";
import { blackhatWorldScrapeTool, blackhatWorldSearchTool, blackhatWorldTrendingTool } from "./tools/blacthatworld/index";
import { githubSearchTool, githubBatchSearchTool } from "./tools/github/index";
import { deepWikiSearchTool, deepWikiBatchSearchTool } from "./tools/deepwiki/index";
import 'dotenv/config';

const server = new FastMCP({
    name: "next-agent",
    version: "1.0.0"
});

// Add all tools to the server
server.addTool(blackhatWorldScrapeTool);
server.addTool(blackhatWorldSearchTool);
server.addTool(blackhatWorldTrendingTool);
server.addTool(githubSearchTool);
server.addTool(githubBatchSearchTool);
server.addTool(deepWikiSearchTool);
server.addTool(deepWikiBatchSearchTool);

server.start({
    transportType: "stdio"
}).catch(error => {
    console.error(`Error running MCP server:`, error);
    process.exit(1);
});
