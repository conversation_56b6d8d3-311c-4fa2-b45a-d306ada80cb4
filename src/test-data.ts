#!/usr/bin/env bun
/**
 * Test Data Generator
 * Adds sample content to the RSS database for testing
 */

import { getContentWriter } from "./tools/rss/content-writer-core.js";
import type { ContentItem } from "./tools/rss/types.js";

const writer = getContentWriter();

// Sample content data
const sampleContent: Array<Omit<ContentItem, 'id' | 'created_at' | 'updated_at'>> = [
  {
    title: "Next.js 15 Released with Major Performance Improvements",
    description: "The latest version of Next.js brings significant performance enhancements and new features for React developers.",
    content: "Next.js 15 introduces several groundbreaking features including improved server components, enhanced image optimization, and better TypeScript support. The new version focuses on developer experience and application performance.",
    url: "https://nextjs.org/blog/next-15",
    author: "Vercel Team",
    published_date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
    source: "github-search",
    tags: ["nextjs", "react", "javascript", "web-development"],
    status: "approved"
  },
  {
    title: "Building Scalable APIs with FastAPI and Python",
    description: "A comprehensive guide to creating high-performance APIs using FastAPI framework.",
    content: "FastAPI has become the go-to framework for building modern APIs in Python. This article covers best practices, performance optimization, and deployment strategies.",
    url: "https://fastapi.tiangolo.com/tutorial/",
    author: "Sebastian Ramirez",
    published_date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
    source: "manual",
    tags: ["python", "fastapi", "api", "backend"],
    status: "approved"
  },
  {
    title: "The Future of AI in Software Development",
    description: "Exploring how artificial intelligence is transforming the way we write and maintain code.",
    content: "AI-powered development tools are revolutionizing software engineering. From code completion to automated testing, AI is becoming an integral part of the development workflow.",
    url: "https://github.com/features/copilot",
    author: "GitHub Team",
    published_date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
    source: "github-search",
    tags: ["ai", "machine-learning", "development-tools", "automation"],
    status: "approved"
  },
  {
    title: "Rust for JavaScript Developers: A Practical Introduction",
    description: "Learn Rust programming language from a JavaScript developer's perspective.",
    content: "Rust offers memory safety and performance that JavaScript developers can leverage for system programming and WebAssembly applications.",
    url: "https://doc.rust-lang.org/book/",
    author: "Rust Foundation",
    published_date: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(), // 4 days ago
    source: "manual",
    tags: ["rust", "javascript", "programming", "webassembly"],
    status: "approved"
  },
  {
    title: "Docker Best Practices for Production Deployments",
    description: "Essential Docker practices for secure and efficient production environments.",
    content: "Learn how to optimize Docker containers for production, including security hardening, multi-stage builds, and resource management.",
    url: "https://docs.docker.com/develop/dev-best-practices/",
    author: "Docker Inc",
    published_date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
    source: "blackhatworld",
    tags: ["docker", "devops", "containers", "deployment"],
    status: "approved"
  },
  {
    title: "Understanding TypeScript Generics with Real Examples",
    description: "Master TypeScript generics through practical examples and use cases.",
    content: "TypeScript generics provide type safety and reusability. This guide covers advanced generic patterns and their practical applications.",
    url: "https://www.typescriptlang.org/docs/handbook/2/generics.html",
    author: "Microsoft TypeScript Team",
    published_date: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString(), // 6 days ago
    source: "github-search",
    tags: ["typescript", "javascript", "programming", "type-safety"],
    status: "approved"
  },
  {
    title: "Building Real-time Applications with WebSockets",
    description: "Create interactive real-time web applications using WebSocket technology.",
    content: "WebSockets enable bidirectional communication between client and server, perfect for chat applications, live updates, and collaborative tools.",
    url: "https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API",
    author: "MDN Contributors",
    published_date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
    source: "manual",
    tags: ["websockets", "real-time", "web-development", "networking"],
    status: "pending" // This one is pending for variety
  },
  {
    title: "GraphQL vs REST: Choosing the Right API Architecture",
    description: "Compare GraphQL and REST APIs to make informed architectural decisions.",
    content: "Both GraphQL and REST have their strengths. This comparison helps you choose the right approach for your specific use case.",
    url: "https://graphql.org/learn/",
    author: "GraphQL Foundation",
    published_date: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000).toISOString(), // 8 days ago
    source: "deepwiki",
    tags: ["graphql", "rest", "api", "architecture"],
    status: "approved"
  }
];

// Categories to create
const categories = [
  { name: "tech", description: "Technology and programming content" },
  { name: "ai", description: "Artificial intelligence and machine learning" },
  { name: "web-dev", description: "Web development tutorials and guides" },
  { name: "devops", description: "DevOps and deployment practices" }
];

async function addSampleData() {
  console.log("🚀 Adding sample data to RSS database...");

  try {
    // Add sample content with different categories
    for (const [index, content] of sampleContent.entries()) {
      let categoryName = "tech"; // default category
      
      // Assign categories based on tags
      if (content.tags?.includes("ai") || content.tags?.includes("machine-learning")) {
        categoryName = "ai";
      } else if (content.tags?.includes("web-development") || content.tags?.includes("javascript")) {
        categoryName = "web-dev";
      } else if (content.tags?.includes("docker") || content.tags?.includes("devops")) {
        categoryName = "devops";
      }

      const result = await writer.writeContent(content, {
        evaluate_quality: false, // Skip LLM evaluation for test data
        auto_approve: content.status === "approved",
        category_name: categoryName,
        tags: content.tags || []
      });

      console.log(`✅ Added: "${result.title}" (ID: ${result.id}, Category: ${categoryName})`);
      
      // Small delay to avoid overwhelming the system
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Get statistics
    const stats = writer.getContentStats();
    
    console.log("\n📊 Sample Data Summary:");
    console.log(`📈 Total Content: ${stats.total}`);
    console.log(`✅ Approved: ${stats.approved}`);
    console.log(`⏳ Pending: ${stats.pending}`);
    console.log(`❌ Rejected: ${stats.rejected}`);
    
    console.log("\n📝 Content by Source:");
    Object.entries(stats.by_source).forEach(([source, count]) => {
      console.log(`  • ${source}: ${count}`);
    });

    console.log("\n🎉 Sample data added successfully!");
    console.log("🔗 Test your RSS feeds:");
    console.log("   • Main feed: http://localhost:3001/feed");
    console.log("   • Tech category: http://localhost:3001/feed/tech");
    console.log("   • AI category: http://localhost:3001/feed/ai");
    console.log("   • Web Dev category: http://localhost:3001/feed/web-dev");
    console.log("   • DevOps category: http://localhost:3001/feed/devops");
    console.log("   • Health check: http://localhost:3001/health");

  } catch (error) {
    console.error("❌ Failed to add sample data:", error);
    process.exit(1);
  }
}

// Run the script
addSampleData();
