#!/usr/bin/env bun
/**
 * Test Markdown Content
 * Adds content with markdown formatting to test HTML conversion
 */

import { getContentWriter } from "./tools/rss/content-writer-core.js";
import type { ContentItem } from "./tools/rss/types.js";

const writer = getContentWriter();

// Sample content with markdown formatting
const markdownContent: Array<Omit<ContentItem, 'id' | 'created_at' | 'updated_at'>> = [
  {
    title: "Advanced TypeScript Patterns for 2025",
    description: "Explore cutting-edge TypeScript patterns and techniques for modern development.",
    content: `# Advanced TypeScript Patterns

TypeScript continues to evolve with powerful features that enable **better type safety** and *developer experience*.

## Key Features

1. **Template Literal Types** - Create dynamic string types
2. **Conditional Types** - Type-level programming
3. **Mapped Types** - Transform existing types

### Code Example

\`\`\`typescript
type EventName<T> = T extends \`on\${infer U}\` ? U : never;
type ClickEvent = EventName<"onClick">; // "Click"
\`\`\`

> **Note**: These patterns require TypeScript 4.5+

## Benefits

- Improved **type inference**
- Better *autocomplete* support
- Reduced runtime errors

[Learn more about TypeScript](https://www.typescriptlang.org/)`,
    url: "https://typescript-patterns.dev/advanced-2025",
    author: "TypeScript Team",
    published_date: new Date().toISOString(),
    source: "manual",
    tags: ["typescript", "programming", "patterns", "advanced"],
    status: "approved"
  },
  {
    title: "Building Microservices with Bun and Elysia",
    description: "A comprehensive guide to creating high-performance microservices using Bun runtime and Elysia framework.",
    content: `# Microservices with Bun & Elysia

## Why Bun + Elysia?

- **Performance**: Bun is significantly faster than Node.js
- **TypeScript First**: Native TypeScript support
- **Modern APIs**: Built for modern JavaScript features

## Getting Started

### Installation

\`\`\`bash
bun add elysia
\`\`\`

### Basic Server

\`\`\`typescript
import { Elysia } from 'elysia'

const app = new Elysia()
  .get('/', () => 'Hello Elysia')
  .get('/json', () => ({ message: 'Hello World' }))
  .listen(3000)

console.log(\`🦊 Elysia is running at \${app.server?.hostname}:\${app.server?.port}\`)
\`\`\`

## Advanced Features

### Validation & Schemas

Elysia provides **built-in validation** with excellent TypeScript integration:

\`\`\`typescript
import { Elysia, t } from 'elysia'

const app = new Elysia()
  .post('/user', ({ body }) => {
    return { success: true, user: body }
  }, {
    body: t.Object({
      name: t.String(),
      email: t.String({ format: 'email' }),
      age: t.Number({ minimum: 0 })
    })
  })
\`\`\`

### Plugins & Middleware

Create reusable plugins:

\`\`\`typescript
const authPlugin = new Elysia()
  .derive(({ headers }) => ({
    user: validateToken(headers.authorization)
  }))

app.use(authPlugin)
\`\`\`

## Performance Benchmarks

| Runtime | Requests/sec | Memory Usage |
|---------|-------------|--------------|
| Bun     | 125,000     | 45MB        |
| Node.js | 85,000      | 78MB        |
| Deno    | 92,000      | 62MB        |

> *Benchmarks may vary based on hardware and application complexity*

## Deployment

### Docker

\`\`\`dockerfile
FROM oven/bun:latest
WORKDIR /app
COPY package.json bun.lockb ./
RUN bun install
COPY . .
EXPOSE 3000
CMD ["bun", "start"]
\`\`\`

### Production Tips

1. **Enable clustering** for multi-core usage
2. **Use HTTP/2** for better performance
3. **Implement proper logging** and monitoring
4. **Set up health checks**

---

*Ready to build lightning-fast microservices? Start with Bun and Elysia today!*`,
    url: "https://bun-microservices.dev/guide",
    author: "Bun Community",
    published_date: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    source: "github-search",
    tags: ["bun", "elysia", "microservices", "typescript", "performance"],
    status: "approved"
  }
];

async function addMarkdownContent() {
  console.log("🚀 Adding markdown content to test HTML conversion...");

  try {
    for (const [index, content] of markdownContent.entries()) {
      const result = await writer.writeContent(content, {
        evaluate_quality: false, // Skip LLM evaluation for test data
        auto_approve: content.status === "approved",
        category_name: "tech",
        tags: content.tags || []
      });

      console.log(`✅ Added: "${result.title}" (ID: ${result.id})`);
      
      // Small delay
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log("\n🎉 Markdown content added successfully!");
    console.log("🔗 Test the RSS feed with HTML formatting:");
    console.log("   • Main feed: http://localhost:3001/feed");
    console.log("   • Tech category: http://localhost:3001/feed/tech");
    console.log("\n📝 The content should now display with proper HTML formatting including:");
    console.log("   • Headers (H1, H2, H3)");
    console.log("   • Bold and italic text");
    console.log("   • Code blocks and inline code");
    console.log("   • Lists and tables");
    console.log("   • Links and blockquotes");

  } catch (error) {
    console.error("❌ Failed to add markdown content:", error);
    process.exit(1);
  }
}

// Run the script
addMarkdownContent();
