/**
 * RSS Server Route Handlers
 * Separated route logic for better maintainability
 */

import { getFeedGenerator } from "./tools/rss/feed-generator.js";
import type { FeedGenerationOptions } from "./tools/rss/types.js";

const generator = getFeedGenerator();

// CORS headers for all responses
export const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type",
};

/**
 * Parse query parameters into feed options
 */
export function parseQueryParams(searchParams: URLSearchParams): FeedGenerationOptions {
  return {
    limit: parseInt(searchParams.get("limit") || "20"),
    include_content: searchParams.get("include_content") !== "false",
    since: searchParams.get("since") || undefined
  };
}

/**
 * Handle RSS feed requests
 */
export async function handleRSSFeed(
  options: FeedGenerationOptions
): Promise<Response> {
  try {
    const rssContent = await generator.generateRSSFeed(options);
    
    return new Response(rssContent, {
      status: 200,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/rss+xml; charset=utf-8",
        "Cache-Control": "public, max-age=300" // 5 minutes cache
      }
    });
  } catch (error) {
    console.error("❌ RSS Feed Generation Error:", error);
    
    if (error instanceof Error && error.message.includes("not found")) {
      return new Response(`Category "${options.category}" not found`, {
        status: 404,
        headers: corsHeaders
      });
    }

    if (error instanceof Error && error.message.includes("No approved content")) {
      return new Response("No content available for feed", {
        status: 404,
        headers: corsHeaders
      });
    }

    return new Response(JSON.stringify({
      error: "Internal Server Error",
      message: error instanceof Error ? error.message : "Unknown error"
    }), {
      status: 500,
      headers: { ...corsHeaders, "Content-Type": "application/json" }
    });
  }
}

/**
 * Handle health check requests
 */
export async function handleHealthCheck(): Promise<Response> {
  return new Response(JSON.stringify({
    status: "healthy",
    timestamp: new Date().toISOString(),
    server: "RSS Feed Server",
    endpoints: ["/feed", "/feed/{category}", "/health"]
  }), {
    status: 200,
    headers: { ...corsHeaders, "Content-Type": "application/json" }
  });
}

/**
 * Handle OPTIONS requests for CORS
 */
export async function handleOptions(): Promise<Response> {
  return new Response(null, { status: 200, headers: corsHeaders });
}

/**
 * Handle method not allowed
 */
export async function handleMethodNotAllowed(): Promise<Response> {
  return new Response("Method Not Allowed", {
    status: 405,
    headers: { ...corsHeaders, "Allow": "GET, OPTIONS" }
  });
}

/**
 * Handle not found
 */
export async function handleNotFound(): Promise<Response> {
  return new Response("Not Found", {
    status: 404,
    headers: corsHeaders
  });
}

/**
 * Route matcher and handler
 */
export interface Route {
  pattern: RegExp;
  handler: (match: RegExpMatchArray, options: FeedGenerationOptions) => Promise<Response>;
}

export const routes: Route[] = [
  {
    pattern: /^\/feed\/(.+)$/,
    handler: async (match, options) => {
      const category = match[1];
      return handleRSSFeed({ ...options, category });
    }
  },
  {
    pattern: /^\/feed\/?$/,
    handler: async (match, options) => {
      return handleRSSFeed(options);
    }
  },
  {
    pattern: /^\/\/?$/,
    handler: async (match, options) => {
      return handleRSSFeed(options);
    }
  },
  {
    pattern: /^\/health\/?$/,
    handler: async () => {
      return handleHealthCheck();
    }
  }
];

/**
 * Route dispatcher
 */
export async function dispatchRoute(
  pathname: string, 
  options: FeedGenerationOptions
): Promise<Response | null> {
  for (const route of routes) {
    const match = pathname.match(route.pattern);
    if (match) {
      return await route.handler(match, options);
    }
  }
  return null; // No route matched
}
