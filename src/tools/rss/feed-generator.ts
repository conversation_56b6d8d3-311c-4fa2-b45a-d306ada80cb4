/**
 * RSS Feed Generator
 * Generates RSS feeds with daily content aggregation and HTML formatting
 */

import { Feed } from "feed";
import { getRSSDatabase } from "./database.js";
import { createDailyDigestHtml } from "./html-formatter.js";
import type { ContentItem, FeedGenerationOptions } from "./types.js";

export class RSSFeedGenerator {
  private db = getRSSDatabase();

  /**
   * Generate RSS feed with daily aggregation
   */
  async generateFeed(options: FeedGenerationOptions = {}): Promise<{
    rss: string;
    atom: string;
    json: string;
    metadata: {
      title: string;
      description: string;
      itemCount: number;
      lastUpdated: Date;
    };
  }> {
    const {
      category,
      limit = 20,
      include_content = true,
      since
    } = options;

    // Get content grouped by date for daily aggregation
    const contentByDate = this.getContentGroupedByDate({
      category,
      limit_days: limit, // Treat limit as number of days instead of items
      since,
      status: 'approved' // Only include approved content in feeds
    });

    if (Object.keys(contentByDate).length === 0) {
      throw new Error("No approved content found for feed generation");
    }

    // Create feed configuration
    const allContent = Object.values(contentByDate).flat() as ContentItem[];
    const feedConfig = this.createFeedConfig(category, allContent);
    const feed = new Feed(feedConfig);

    // Create daily digest items
    const sortedDates = Object.keys(contentByDate).sort().reverse(); // Most recent first

    for (const date of sortedDates) {
      const dailyContent = contentByDate[date];
      if (dailyContent.length > 0) {
        const digestItem = this.createDailyDigestItem(date, dailyContent, include_content);
        feed.addItem(digestItem);
      }
    }

    return {
      rss: feed.rss2(),
      atom: feed.atom1(),
      json: feed.json1(),
      metadata: {
        title: feedConfig.title,
        description: feedConfig.description,
        itemCount: sortedDates.length, // Number of daily digest items
        lastUpdated: new Date()
      }
    };
  }

  /**
   * Generate RSS 2.0 feed only
   */
  async generateRSSFeed(options: FeedGenerationOptions = {}): Promise<string> {
    const result = await this.generateFeed(options);
    return result.rss;
  }

  /**
   * Generate Atom feed only
   */
  async generateAtomFeed(options: FeedGenerationOptions = {}): Promise<string> {
    const result = await this.generateFeed(options);
    return result.atom;
  }

  /**
   * Generate JSON feed only
   */
  async generateJSONFeed(options: FeedGenerationOptions = {}): Promise<string> {
    const result = await this.generateFeed(options);
    return result.json;
  }

  /**
   * Get content grouped by date for daily aggregation
   */
  private getContentGroupedByDate(options: {
    category?: string;
    limit_days?: number;
    since?: Date | string;
    status: string;
  }): Record<string, ContentItem[]> {
    let categoryId: number | undefined;

    // Get category ID if category name provided
    if (options.category) {
      const categories = this.db.getCategories();
      const category = categories.find(c => c.name.toLowerCase() === options.category!.toLowerCase());
      if (!category) {
        throw new Error(`Category "${options.category}" not found`);
      }
      categoryId = category.id;
    }

    return this.db.getContentGroupedByDate({
      category_id: categoryId,
      status: options.status,
      limit_days: options.limit_days,
      since: options.since
    });
  }

  /**
   * Get content for feed generation (legacy method for backward compatibility)
   */
  private getContentForFeed(options: {
    category?: string;
    limit: number;
    since?: Date | string;
    status: string;
  }): ContentItem[] {
    let categoryId: number | undefined;

    // Get category ID if category name provided
    if (options.category) {
      const categories = this.db.getCategories();
      const category = categories.find(c => c.name.toLowerCase() === options.category!.toLowerCase());
      if (!category) {
        throw new Error(`Category "${options.category}" not found`);
      }
      categoryId = category.id;
    }

    return this.db.getContent({
      category_id: categoryId,
      status: options.status,
      limit: options.limit,
      since: options.since,
      order_by: 'created_at DESC'
    });
  }

  /**
   * Create feed configuration
   */
  private createFeedConfig(category: string | undefined, content: ContentItem[]) {
    const baseUrl = process.env.RSS_BASE_URL || "http://localhost:3001";
    const siteName = process.env.RSS_SITE_NAME || "RSS Content Feed";
    const siteDescription = process.env.RSS_SITE_DESCRIPTION || "Curated content feed";

    const title = category ? `${siteName} - ${category}` : siteName;
    const description = category
      ? `${siteDescription} - ${category} category`
      : siteDescription;

    const feedUrl = category
      ? `${baseUrl}/feed/${category}`
      : `${baseUrl}/feed`;

    return {
      title,
      description,
      id: baseUrl,
      link: baseUrl,
      language: "en",
      image: `${baseUrl}/favicon.ico`,
      favicon: `${baseUrl}/favicon.ico`,
      copyright: `© ${new Date().getFullYear()} ${siteName}`,
      updated: content.length > 0 ? new Date(content[0].created_at!) : new Date(),
      generator: "RSS Content System",
      feedLinks: {
        rss2: feedUrl,
        atom: feedUrl.replace('/feed', '/atom'),
        json: feedUrl.replace('/feed', '/json')
      },
      author: {
        name: siteName,
        email: process.env.RSS_AUTHOR_EMAIL || "<EMAIL>",
        link: baseUrl
      }
    };
  }

  /**
   * Create daily digest feed item from multiple content items
   */
  private createDailyDigestItem(date: string, items: ContentItem[], includeContent: boolean) {
    const baseUrl = process.env.RSS_BASE_URL || "http://localhost:3001";

    // Create daily digest HTML
    const digest = createDailyDigestHtml(date, items);

    // Get the most recent item for date reference
    const latestItem = items.reduce((latest, current) => {
      const latestDate = new Date(latest.created_at || latest.published_date || 0);
      const currentDate = new Date(current.created_at || current.published_date || 0);
      return currentDate > latestDate ? current : latest;
    });

    // Collect all unique tags and sources
    const allTags = [...new Set(items.flatMap(item => item.tags || []))];
    const allSources = [...new Set(items.map(item => item.source))];
    const allAuthors = [...new Set(items.map(item => item.author).filter(Boolean))];

    return {
      title: digest.title,
      id: `${baseUrl}/digest/${date}`,
      link: `${baseUrl}/digest/${date}`,
      description: digest.description,
      content: includeContent ? digest.content : digest.description,
      author: allAuthors.length > 0 ? allAuthors.map(name => ({ name })) : undefined,
      date: new Date(latestItem.created_at || latestItem.published_date || date),
      category: allTags.map(tag => ({ name: tag })),
      guid: `${baseUrl}/digest/${date}`,
      published: new Date(latestItem.created_at || latestItem.published_date || date),
      // Add custom elements for RSS
      custom_elements: [
        { 'content:encoded': digest.content },
        { 'dc:creator': allAuthors.join(', ') || 'RSS Content System' },
        { 'source': allSources.join(', ') },
        { 'itemCount': items.length.toString() }
      ]
    };
  }

  /**
   * Create feed item from content (legacy method for backward compatibility)
   */
  private createFeedItem(item: ContentItem, includeContent: boolean) {
    const baseUrl = process.env.RSS_BASE_URL || "http://localhost:3001";

    return {
      title: item.title,
      id: item.url,
      link: item.url,
      description: item.description || item.title,
      content: includeContent ? item.content : undefined,
      author: item.author ? [{ name: item.author }] : undefined,
      date: new Date(item.published_date || item.created_at!),
      category: item.tags ? item.tags.map(tag => ({ name: tag })) : undefined,
      guid: `${baseUrl}/content/${item.id}`,
      published: new Date(item.published_date || item.created_at!)
    };
  }

  /**
   * Get feed statistics
   */
  getFeedStats(category?: string): {
    totalItems: number;
    approvedItems: number;
    pendingItems: number;
    rejectedItems: number;
    lastUpdated: Date | null;
    categories: Array<{ name: string; count: number }>;
  } {
    const db = this.db.getDatabase();

    let categoryFilter = "";
    const params: any[] = [];

    if (category) {
      categoryFilter = `
        JOIN categories cat ON c.category_id = cat.id 
        WHERE cat.name = ?
      `;
      params.push(category);
    }

    const totalQuery = `SELECT COUNT(*) as count FROM content c ${categoryFilter}`;
    const approvedQuery = `SELECT COUNT(*) as count FROM content c ${categoryFilter} ${category ? 'AND' : 'WHERE'} c.status = 'approved'`;
    const pendingQuery = `SELECT COUNT(*) as count FROM content c ${categoryFilter} ${category ? 'AND' : 'WHERE'} c.status = 'pending'`;
    const rejectedQuery = `SELECT COUNT(*) as count FROM content c ${categoryFilter} ${category ? 'AND' : 'WHERE'} c.status = 'rejected'`;
    const lastUpdatedQuery = `SELECT MAX(c.created_at) as last_updated FROM content c ${categoryFilter}`;

    const total = db.prepare(totalQuery).get(...params) as { count: number };
    const approved = db.prepare(approvedQuery).get(...params) as { count: number };
    const pending = db.prepare(pendingQuery).get(...params) as { count: number };
    const rejected = db.prepare(rejectedQuery).get(...params) as { count: number };
    const lastUpdated = db.prepare(lastUpdatedQuery).get(...params) as { last_updated: string | null };

    // Get category statistics
    const categoryStats = db.prepare(`
      SELECT cat.name, COUNT(c.id) as count
      FROM categories cat
      LEFT JOIN content c ON cat.id = c.category_id AND c.status = 'approved'
      GROUP BY cat.id, cat.name
      ORDER BY count DESC
    `).all() as Array<{ name: string; count: number }>;

    return {
      totalItems: total.count,
      approvedItems: approved.count,
      pendingItems: pending.count,
      rejectedItems: rejected.count,
      lastUpdated: lastUpdated.last_updated ? new Date(lastUpdated.last_updated) : null,
      categories: categoryStats
    };
  }

  /**
   * Validate feed content
   */
  validateFeedContent(content: ContentItem[]): {
    valid: boolean;
    issues: string[];
    warnings: string[];
  } {
    const issues: string[] = [];
    const warnings: string[] = [];

    if (content.length === 0) {
      issues.push("No content items provided");
      return { valid: false, issues, warnings };
    }

    for (const item of content) {
      // Required fields
      if (!item.title || item.title.trim().length === 0) {
        issues.push(`Item ${item.id}: Missing or empty title`);
      }

      if (!item.url) {
        issues.push(`Item ${item.id}: Missing URL`);
      }

      // Warnings for missing optional but recommended fields
      if (!item.description) {
        warnings.push(`Item ${item.id}: Missing description`);
      }

      if (!item.published_date && !item.created_at) {
        warnings.push(`Item ${item.id}: Missing publication date`);
      }

      if (!item.author) {
        warnings.push(`Item ${item.id}: Missing author`);
      }
    }

    return {
      valid: issues.length === 0,
      issues,
      warnings
    };
  }
}

// Singleton instance
let generatorInstance: RSSFeedGenerator | null = null;

export function getFeedGenerator(): RSSFeedGenerator {
  if (!generatorInstance) {
    generatorInstance = new RSSFeedGenerator();
  }
  return generatorInstance;
}
