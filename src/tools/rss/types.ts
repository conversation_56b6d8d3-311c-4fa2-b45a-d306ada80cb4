/**
 * RSS System Type Definitions
 */

export interface ContentItem {
  id?: number;
  title: string;
  description?: string;
  content?: string;
  url: string;
  author?: string;
  published_date?: Date | string;
  created_at?: Date | string;
  updated_at?: Date | string;
  category_id?: number;
  tags?: string[];
  source: string; // which tool/method discovered this content
  quality_score?: number; // LLM evaluation score (0-1)
  status?: 'pending' | 'approved' | 'rejected';
}

export interface Category {
  id?: number;
  name: string;
  description?: string;
  rss_enabled?: boolean;
  created_at?: Date | string;
}

export interface RSSFeed {
  id?: number;
  name: string;
  description?: string;
  category_id?: number; // null for all categories
  max_items?: number;
  include_content?: boolean;
  created_at?: Date | string;
  updated_at?: Date | string;
}

export interface ContentWriterOptions {
  evaluate_quality?: boolean;
  auto_approve?: boolean;
  category_name?: string;
  tags?: string[];
}

export interface FeedGenerationOptions {
  category?: string;
  limit?: number; // For daily aggregation, this represents number of days
  format?: 'rss' | 'atom' | 'json';
  include_content?: boolean;
  since?: Date | string;
  daily_aggregation?: boolean; // Enable/disable daily aggregation (default: true)
}

export interface DatabaseConfig {
  path: string;
  init?: boolean;
}
