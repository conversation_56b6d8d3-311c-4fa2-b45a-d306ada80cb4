/**
 * Core Content Writer - Universal content writing methods
 * Can be used by MCP tools, HTTP APIs, or other integrations
 */

import { generateText } from "ai";
import { openai } from "@ai-sdk/openai";
import pRetry from "p-retry";
import { getRSSDatabase } from "./database.js";
import type { ContentItem, ContentWriterOptions } from "./types.js";

export class ContentWriterCore {
  private db = getRSSDatabase();

  /**
   * Universal content writing method
   * This is the core method that all other writing methods should use
   */
  async writeContent(
    item: Omit<ContentItem, 'id' | 'created_at' | 'updated_at'>,
    options: ContentWriterOptions = {}
  ): Promise<ContentItem> {
    try {
      // Apply defaults
      const finalOptions: Required<ContentWriterOptions> = {
        evaluate_quality: true,
        auto_approve: false,
        category_name: 'general',
        tags: [],
        ...options
      };

      // Get or create category
      let categoryId: number | undefined;
      if (finalOptions.category_name) {
        const category = this.db.getOrCreateCategory(finalOptions.category_name);
        categoryId = category.id;
      }

      // Prepare content item
      let contentItem: ContentItem = {
        ...item,
        category_id: categoryId,
        tags: [...(item.tags || []), ...finalOptions.tags],
        status: finalOptions.auto_approve ? 'approved' : 'pending'
      };

      // Evaluate content quality if requested
      if (finalOptions.evaluate_quality && process.env.OPENAI_API_KEY) {
        try {
          const qualityScore = await this.evaluateContentQuality(contentItem);
          contentItem.quality_score = qualityScore;

          // Auto-approve high-quality content if enabled
          if (finalOptions.auto_approve && qualityScore >= 0.7) {
            contentItem.status = 'approved';
          }
        } catch (error) {
          console.warn("⚠️ Content quality evaluation failed:", error);
          // Continue without quality score
        }
      }

      // Write to database with retry logic
      const savedContent = await pRetry(
        () => this.db.insertContent(contentItem),
        {
          retries: 3,
          factor: 2,
          minTimeout: 1000,
          onFailedAttempt: (error) => {
            console.warn(`💾 Database write attempt ${error.attemptNumber} failed:`, error.message);
          }
        }
      );

      console.log(`✅ Content saved: "${savedContent.title}" (ID: ${savedContent.id})`);
      return savedContent;

    } catch (error) {
      console.error("❌ Failed to write content:", error);
      throw error;
    }
  }

  /**
   * Batch write multiple content items
   */
  async writeContentBatch(
    items: Array<Omit<ContentItem, 'id' | 'created_at' | 'updated_at'>>,
    options: ContentWriterOptions = {}
  ): Promise<ContentItem[]> {
    const results: ContentItem[] = [];
    const errors: Array<{ item: any; error: Error }> = [];

    console.log(`📝 Starting batch write of ${items.length} items`);

    for (const item of items) {
      try {
        const result = await this.writeContent(item, options);
        results.push(result);
      } catch (error) {
        console.error(`❌ Failed to write item "${item.title}":`, error);
        errors.push({ item, error: error as Error });
      }
    }

    console.log(`✅ Batch write completed: ${results.length} success, ${errors.length} failures`);

    if (errors.length > 0) {
      console.warn("⚠️ Some items failed to write:", errors);
    }

    return results;
  }

  /**
   * Write content from URL (for web scraping integrations)
   */
  async writeContentFromUrl(
    url: string,
    source: string,
    options: ContentWriterOptions & {
      title?: string;
      description?: string;
      content?: string;
      author?: string;
    } = {}
  ): Promise<ContentItem> {
    const contentItem: Omit<ContentItem, 'id' | 'created_at' | 'updated_at'> = {
      title: options.title || `Content from ${new URL(url).hostname}`,
      description: options.description,
      content: options.content,
      url,
      author: options.author,
      source,
      published_date: new Date().toISOString()
    };

    return this.writeContent(contentItem, options);
  }

  /**
   * Evaluate content quality using LLM
   */
  private async evaluateContentQuality(item: ContentItem): Promise<number> {
    if (!process.env.OPENAI_API_KEY) {
      throw new Error("OpenAI API key not configured");
    }

    const prompt = `
Evaluate the quality of this content on a scale of 0.0 to 1.0:

Title: ${item.title}
Description: ${item.description || 'N/A'}
URL: ${item.url}
Source: ${item.source}

Consider:
- Relevance and value to readers
- Content uniqueness and originality  
- Information quality and accuracy
- Potential spam or low-quality indicators

Respond with only a number between 0.0 and 1.0 (e.g., 0.8)
`;

    const result = await generateText({
      model: openai("gpt-3.5-turbo"),
      prompt,
      maxTokens: 10,
      temperature: 0.1
    });

    const score = parseFloat(result.text.trim());
    
    if (isNaN(score) || score < 0 || score > 1) {
      throw new Error(`Invalid quality score: ${result.text}`);
    }

    return score;
  }

  /**
   * Update content status (approve/reject)
   */
  async updateContentStatus(
    contentId: number,
    status: 'pending' | 'approved' | 'rejected'
  ): Promise<void> {
    const db = this.db.getDatabase();
    const stmt = db.prepare("UPDATE content SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
    const result = stmt.run(status, contentId);

    if (result.changes === 0) {
      throw new Error(`Content with ID ${contentId} not found`);
    }

    console.log(`✅ Content ${contentId} status updated to: ${status}`);
  }

  /**
   * Get content statistics
   */
  getContentStats(): {
    total: number;
    pending: number;
    approved: number;
    rejected: number;
    by_source: Record<string, number>;
  } {
    const db = this.db.getDatabase();
    
    const total = db.prepare("SELECT COUNT(*) as count FROM content").get() as { count: number };
    const pending = db.prepare("SELECT COUNT(*) as count FROM content WHERE status = 'pending'").get() as { count: number };
    const approved = db.prepare("SELECT COUNT(*) as count FROM content WHERE status = 'approved'").get() as { count: number };
    const rejected = db.prepare("SELECT COUNT(*) as count FROM content WHERE status = 'rejected'").get() as { count: number };
    
    const bySource = db.prepare(`
      SELECT source, COUNT(*) as count 
      FROM content 
      GROUP BY source 
      ORDER BY count DESC
    `).all() as Array<{ source: string; count: number }>;

    return {
      total: total.count,
      pending: pending.count,
      approved: approved.count,
      rejected: rejected.count,
      by_source: Object.fromEntries(bySource.map(s => [s.source, s.count]))
    };
  }
}

// Singleton instance
let writerInstance: ContentWriterCore | null = null;

export function getContentWriter(): ContentWriterCore {
  if (!writerInstance) {
    writerInstance = new ContentWriterCore();
  }
  return writerInstance;
}
