/**
 * Content Writer MCP Tool
 * Provides MCP interface for storing discovered content
 */

import { z } from "zod";
import { getContentWriter } from "./content-writer-core.js";
import type { ContentItem } from "./types.js";

// Input schema for content writing
const contentWriterSchema = z.object({
  title: z.string().min(1).describe("Content title (required)"),
  description: z.string().optional().describe("Content description or summary"),
  content: z.string().optional().describe("Full content text"),
  url: z.string().url().describe("Source URL (must be valid URL)"),
  author: z.string().optional().describe("Content author"),
  published_date: z.string().optional().describe("Publication date (ISO format)"),
  source: z.string().min(1).describe("Source identifier (e.g., 'github-search', 'blackhatworld', 'manual')"),
  category: z.string().optional().describe("Content category name (will be created if doesn't exist)"),
  tags: z.array(z.string()).optional().describe("Content tags"),
  evaluate_quality: z.boolean().default(true).describe("Whether to evaluate content quality using LLM"),
  auto_approve: z.boolean().default(false).describe("Auto-approve high-quality content (quality_score >= 0.7)")
});

// Batch writing schema
const batchContentWriterSchema = z.object({
  items: z.array(z.object({
    title: z.string().min(1),
    description: z.string().optional(),
    content: z.string().optional(),
    url: z.string().url(),
    author: z.string().optional(),
    published_date: z.string().optional(),
    source: z.string().min(1),
    tags: z.array(z.string()).optional()
  })).min(1).max(10).describe("Array of content items to write (max 10 items)"),
  category: z.string().optional().describe("Category for all items"),
  evaluate_quality: z.boolean().default(true).describe("Whether to evaluate content quality using LLM"),
  auto_approve: z.boolean().default(false).describe("Auto-approve high-quality content")
});

// Remove URL-based and status schemas - keeping only essential tools

// ============================================================================
// Tool Implementations
// ============================================================================

/**
 * Main content writer tool
 */
export const contentWriterTool = {
  name: "rss-content-writer",
  description: "Store discovered content in RSS database with optional LLM quality evaluation. Supports automatic categorization and quality scoring.",
  parameters: contentWriterSchema,
  execute: async (args: z.infer<typeof contentWriterSchema>) => {
    const writer = getContentWriter();

    try {
      const { title, description, content, url, author, published_date, source, category, tags, evaluate_quality, auto_approve } = args;

      const contentItem: Omit<ContentItem, 'id' | 'created_at' | 'updated_at'> = {
        title,
        description,
        content,
        url,
        author,
        published_date: published_date ? new Date(published_date).toISOString() : new Date().toISOString(),
        source,
        tags
      };

      const result = await writer.writeContent(contentItem, {
        evaluate_quality,
        auto_approve,
        category_name: category,
        tags: tags || []
      });

      return `✅ Content saved successfully!
📝 Title: ${result.title}
🆔 ID: ${result.id}
🔗 URL: ${result.url}
📂 Category ID: ${result.category_id || 'N/A'}
⭐ Quality Score: ${result.quality_score ? result.quality_score.toFixed(2) : 'N/A'}
📊 Status: ${result.status}

Content "${result.title}" has been stored in the RSS database.`;

    } catch (error) {
      console.error("❌ Content writer tool failed:", error);
      return `❌ Failed to save content: ${error instanceof Error ? error.message : "Unknown error occurred"}`;
    }
  }
};

/**
 * Batch content writer tool
 */
export const batchContentWriterTool = {
  name: "rss-batch-content-writer",
  description: "Write multiple content items to RSS database in batch. Useful for bulk content import.",
  parameters: batchContentWriterSchema,
  execute: async (args: z.infer<typeof batchContentWriterSchema>) => {
    const writer = getContentWriter();

    try {
      const { items, category, evaluate_quality, auto_approve } = args;

      const contentItems = items.map(item => ({
        ...item,
        published_date: item.published_date ? new Date(item.published_date).toISOString() : new Date().toISOString()
      }));

      const results = await writer.writeContentBatch(contentItems, {
        evaluate_quality,
        auto_approve,
        category_name: category
      });

      return `✅ Batch content write completed!
📊 Total items: ${items.length}
✅ Saved items: ${results.length}
❌ Failed items: ${items.length - results.length}
🆔 Content IDs: ${results.map(r => r.id).join(', ')}

Batch write completed: ${results.length}/${items.length} items saved successfully.`;

    } catch (error) {
      console.error("❌ Batch content writer tool failed:", error);
      return `❌ Failed to save batch content: ${error instanceof Error ? error.message : "Unknown error occurred"}`;
    }
  }
};

// URL content writer tool removed - use basic contentWriterTool instead

// Content status and stats tools removed - keeping only essential content writing tools
