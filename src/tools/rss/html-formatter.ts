/**
 * HTML Content Formatter
 * Handles markdown-to-HTML conversion and RSS-optimized HTML formatting
 */

import MarkdownIt from 'markdown-it';
import type { ContentItem } from './types.js';

// Configure markdown-it with RSS-friendly options
const md = new MarkdownIt({
  html: true,        // Enable HTML tags in source
  xhtmlOut: true,    // Use '/' to close single tags (<br />)
  breaks: true,      // Convert '\n' in paragraphs into <br>
  linkify: true,     // Autoconvert URL-like text to links
  typographer: true  // Enable some language-neutral replacement + quotes beautification
});

/**
 * Convert markdown content to HTML
 */
export function markdownToHtml(markdown: string): string {
  if (!markdown || markdown.trim() === '') {
    return '';
  }
  
  return md.render(markdown);
}

/**
 * Format content item as HTML for RSS feeds
 */
export function formatContentItemAsHtml(item: ContentItem): string {
  const title = escapeHtml(item.title);
  const description = item.description ? escapeHtml(item.description) : '';
  const author = item.author ? escapeHtml(item.author) : '';
  const url = escapeHtml(item.url);
  const source = escapeHtml(item.source);
  const tags = item.tags || [];
  
  // Convert content from markdown to HTML if it exists
  let contentHtml = '';
  if (item.content) {
    // Check if content looks like markdown (contains markdown syntax)
    if (isMarkdown(item.content)) {
      contentHtml = markdownToHtml(item.content);
    } else {
      // Treat as plain text and wrap in paragraphs
      contentHtml = `<p>${escapeHtml(item.content).replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>')}</p>`;
    }
  }

  // Build HTML structure optimized for RSS readers
  let html = `
<div class="rss-content-item" style="margin-bottom: 2em; padding: 1em; border-left: 3px solid #007acc; background-color: #f8f9fa;">
  <h3 style="margin-top: 0; color: #333; font-size: 1.2em;">
    <a href="${url}" style="color: #007acc; text-decoration: none;">${title}</a>
  </h3>`;

  if (description) {
    html += `
  <p style="color: #666; font-style: italic; margin: 0.5em 0;">${description}</p>`;
  }

  if (contentHtml) {
    html += `
  <div class="content" style="margin: 1em 0; line-height: 1.6;">
    ${contentHtml}
  </div>`;
  }

  // Add metadata section
  html += `
  <div class="metadata" style="margin-top: 1em; padding-top: 0.5em; border-top: 1px solid #eee; font-size: 0.9em; color: #888;">`;

  if (author) {
    html += `<span style="margin-right: 1em;">👤 <strong>Author:</strong> ${author}</span>`;
  }

  html += `<span style="margin-right: 1em;">📡 <strong>Source:</strong> ${source}</span>`;

  if (tags.length > 0) {
    const tagHtml = tags.map(tag => `<span style="background: #e1f5fe; color: #0277bd; padding: 2px 6px; border-radius: 3px; font-size: 0.8em;">${escapeHtml(tag)}</span>`).join(' ');
    html += `<div style="margin-top: 0.5em;">🏷️ <strong>Tags:</strong> ${tagHtml}</div>`;
  }

  html += `
    <div style="margin-top: 0.5em;">
      <a href="${url}" style="color: #007acc; text-decoration: none; font-weight: bold;">🔗 Read Full Article</a>
    </div>
  </div>
</div>`;

  return html;
}

/**
 * Create daily digest HTML from multiple content items
 */
export function createDailyDigestHtml(date: string, items: ContentItem[]): {
  title: string;
  content: string;
  description: string;
} {
  const formattedDate = formatDateForDisplay(date);
  const title = `Daily Content Digest - ${formattedDate}`;
  
  // Create summary description
  const itemCount = items.length;
  const sources = [...new Set(items.map(item => item.source))];
  const categories = [...new Set(items.map(item => item.tags || []).flat())];
  
  const description = `${itemCount} content item${itemCount !== 1 ? 's' : ''} from ${sources.length} source${sources.length !== 1 ? 's' : ''}: ${sources.join(', ')}`;

  // Build comprehensive HTML content
  let html = `
<div class="daily-digest" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 800px; margin: 0 auto;">
  <header style="text-align: center; margin-bottom: 2em; padding: 1.5em; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px;">
    <h1 style="margin: 0; font-size: 1.8em;">📰 Daily Content Digest</h1>
    <h2 style="margin: 0.5em 0 0 0; font-size: 1.3em; opacity: 0.9;">${formattedDate}</h2>
    <p style="margin: 0.5em 0 0 0; opacity: 0.8;">${description}</p>
  </header>

  <div class="digest-summary" style="background: #f8f9fa; padding: 1em; border-radius: 6px; margin-bottom: 2em;">
    <h3 style="margin-top: 0; color: #333;">📊 Today's Summary</h3>
    <ul style="margin: 0; padding-left: 1.5em;">
      <li><strong>${itemCount}</strong> content item${itemCount !== 1 ? 's' : ''} discovered</li>
      <li><strong>${sources.length}</strong> source${sources.length !== 1 ? 's' : ''}: ${sources.join(', ')}</li>`;

  if (categories.length > 0) {
    html += `<li><strong>Topics:</strong> ${categories.slice(0, 10).join(', ')}${categories.length > 10 ? '...' : ''}</li>`;
  }

  html += `
    </ul>
  </div>

  <div class="content-items">`;

  // Add each content item
  items.forEach((item, index) => {
    html += `
    <article style="margin-bottom: 2em;">
      <h3 style="color: #333; border-bottom: 2px solid #007acc; padding-bottom: 0.5em;">
        📄 Article ${index + 1}: ${escapeHtml(item.title)}
      </h3>
      ${formatContentItemAsHtml(item)}
    </article>`;
  });

  html += `
  </div>

  <footer style="text-align: center; margin-top: 3em; padding: 1em; background: #f0f0f0; border-radius: 6px; color: #666;">
    <p style="margin: 0;">Generated by RSS Content System • ${new Date().toISOString()}</p>
  </footer>
</div>`;

  return {
    title,
    content: html,
    description
  };
}

/**
 * Escape HTML special characters
 */
function escapeHtml(text: string): string {
  const map: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;'
  };
  return text.replace(/[&<>"']/g, (m) => map[m]);
}

/**
 * Check if content appears to be markdown
 */
function isMarkdown(content: string): boolean {
  // Simple heuristics to detect markdown
  const markdownPatterns = [
    /^#{1,6}\s/m,           // Headers
    /\*\*.*\*\*/,           // Bold
    /\*.*\*/,               // Italic
    /\[.*\]\(.*\)/,         // Links
    /^[-*+]\s/m,            // Lists
    /^>\s/m,                // Blockquotes
    /```[\s\S]*```/,        // Code blocks
    /`.*`/                  // Inline code
  ];
  
  return markdownPatterns.some(pattern => pattern.test(content));
}

/**
 * Format date for display
 */
function formatDateForDisplay(dateString: string): string {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch {
    return dateString;
  }
}
