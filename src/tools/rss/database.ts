/**
 * RSS Database Connection and Schema Management
 * Using Bun's built-in SQLite for optimal performance
 */

import { Database } from "bun:sqlite";
import type { ContentItem, Category, DatabaseConfig } from "./types.js";

export class RSSDatabase {
  private db: Database;

  constructor(config: DatabaseConfig = { path: "rss-content.db", init: true }) {
    this.db = new Database(config.path);

    if (config.init) {
      this.initializeSchema();
    }
  }

  /**
   * Initialize database schema
   */
  private initializeSchema(): void {
    // Enable foreign keys
    this.db.exec("PRAGMA foreign_keys = ON");

    // Categories table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        description TEXT,
        rss_enabled BOOLEAN DEFAULT true,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Content table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS content (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT,
        content TEXT,
        url TEXT UNIQUE NOT NULL,
        author TEXT,
        published_date DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        category_id INTEGER,
        tags TEXT, -- JSON array
        source TEXT NOT NULL,
        quality_score REAL,
        status TEXT DEFAULT 'pending',
        FOREIGN KEY (category_id) REFERENCES categories (id)
      )
    `);

    // RSS feeds configuration table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS rss_feeds (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        category_id INTEGER,
        max_items INTEGER DEFAULT 20,
        include_content BOOLEAN DEFAULT true,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories (id)
      )
    `);

    // Create indexes for better performance
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_content_created_at ON content(created_at);
      CREATE INDEX IF NOT EXISTS idx_content_category_id ON content(category_id);
      CREATE INDEX IF NOT EXISTS idx_content_status ON content(status);
      CREATE INDEX IF NOT EXISTS idx_content_url ON content(url);
    `);

    // Insert default category if not exists
    this.db.exec(`
      INSERT OR IGNORE INTO categories (name, description) 
      VALUES ('general', 'General content category')
    `);

    console.log("✅ RSS Database schema initialized");
  }

  /**
   * Get or create category by name
   */
  getOrCreateCategory(name: string, description?: string): Category {
    const existing = this.db.prepare("SELECT * FROM categories WHERE name = ?").get(name) as Category | undefined;

    if (existing) {
      return existing;
    }

    const result = this.db.prepare(`
      INSERT INTO categories (name, description) 
      VALUES (?, ?) 
      RETURNING *
    `).get(name, description || null) as Category;

    return result;
  }

  /**
   * Insert content item
   */
  insertContent(item: ContentItem): ContentItem {
    const stmt = this.db.prepare(`
      INSERT INTO content (
        title, description, content, url, author, published_date,
        category_id, tags, source, quality_score, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      RETURNING *
    `);

    const result = stmt.get(
      item.title,
      item.description || null,
      item.content || null,
      item.url,
      item.author || null,
      item.published_date ? (typeof item.published_date === 'string' ? item.published_date : item.published_date.toISOString()) : null,
      item.category_id || null,
      item.tags ? JSON.stringify(item.tags) : null,
      item.source,
      item.quality_score || null,
      item.status || 'pending'
    ) as ContentItem;

    return result;
  }

  /**
   * Get content items with filtering
   */
  getContent(options: {
    category_id?: number;
    status?: string;
    limit?: number;
    since?: Date | string;
    order_by?: string;
  } = {}): ContentItem[] {
    let query = `
      SELECT c.*, cat.name as category_name
      FROM content c
      LEFT JOIN categories cat ON c.category_id = cat.id
      WHERE 1=1
    `;
    const params: any[] = [];

    if (options.category_id) {
      query += " AND c.category_id = ?";
      params.push(options.category_id);
    }

    if (options.status) {
      query += " AND c.status = ?";
      params.push(options.status);
    }

    if (options.since) {
      query += " AND c.created_at >= ?";
      params.push(options.since);
    }

    query += ` ORDER BY ${options.order_by || 'c.created_at DESC'}`;

    if (options.limit) {
      query += " LIMIT ?";
      params.push(options.limit);
    }

    const stmt = this.db.prepare(query);
    const results = stmt.all(...params) as ContentItem[];

    // Parse tags JSON
    return results.map(item => ({
      ...item,
      tags: item.tags ? JSON.parse(item.tags as string) : [] as string[]
    }));
  }

  /**
   * Get content grouped by date for daily aggregation
   */
  getContentGroupedByDate(options: {
    category_id?: number;
    status?: string;
    limit_days?: number;
    since?: Date | string;
  } = {}): Record<string, ContentItem[]> {
    let query = `
      SELECT c.*, cat.name as category_name,
             DATE(c.created_at) as content_date
      FROM content c
      LEFT JOIN categories cat ON c.category_id = cat.id
      WHERE 1=1
    `;
    const params: any[] = [];

    if (options.category_id) {
      query += " AND c.category_id = ?";
      params.push(options.category_id);
    }

    if (options.status) {
      query += " AND c.status = ?";
      params.push(options.status);
    }

    if (options.since) {
      query += " AND c.created_at >= ?";
      params.push(options.since);
    }

    query += " ORDER BY c.created_at DESC";

    if (options.limit_days) {
      // Limit to recent days by adding a date filter
      query += ` LIMIT ${options.limit_days * 50}`; // Rough estimate to get enough items
    }

    const stmt = this.db.prepare(query);
    const results = stmt.all(...params) as (ContentItem & { content_date: string })[];

    // Group by date
    const grouped: Record<string, ContentItem[]> = {};

    for (const item of results) {
      const date = item.content_date;
      if (!grouped[date]) {
        grouped[date] = [];
      }

      // Parse tags and remove the content_date field
      const { content_date, ...contentItem } = item;
      grouped[date].push({
        ...contentItem,
        tags: contentItem.tags ? JSON.parse(contentItem.tags as string) : [] as string[]
      });
    }

    // If limit_days is specified, only return the most recent days
    if (options.limit_days) {
      const sortedDates = Object.keys(grouped).sort().reverse();
      const limitedGrouped: Record<string, ContentItem[]> = {};

      for (let i = 0; i < Math.min(options.limit_days, sortedDates.length); i++) {
        const date = sortedDates[i];
        limitedGrouped[date] = grouped[date];
      }

      return limitedGrouped;
    }

    return grouped;
  }

  /**
   * Get available content dates for pagination
   */
  getContentDates(options: {
    category_id?: number;
    status?: string;
    since?: Date | string;
  } = {}): string[] {
    let query = `
      SELECT DISTINCT DATE(c.created_at) as content_date
      FROM content c
      WHERE 1=1
    `;
    const params: any[] = [];

    if (options.category_id) {
      query += " AND c.category_id = ?";
      params.push(options.category_id);
    }

    if (options.status) {
      query += " AND c.status = ?";
      params.push(options.status);
    }

    if (options.since) {
      query += " AND c.created_at >= ?";
      params.push(options.since);
    }

    query += " ORDER BY content_date DESC";

    const stmt = this.db.prepare(query);
    const results = stmt.all(...params) as { content_date: string }[];

    return results.map(row => row.content_date);
  }

  /**
   * Get all categories
   */
  getCategories(): Category[] {
    return this.db.prepare("SELECT * FROM categories ORDER BY name").all() as Category[];
  }

  /**
   * Close database connection
   */
  close(): void {
    this.db.close();
  }

  /**
   * Get database instance for custom queries
   */
  getDatabase(): Database {
    return this.db;
  }
}

// Singleton instance
let dbInstance: RSSDatabase | null = null;

export function getRSSDatabase(config?: DatabaseConfig): RSSDatabase {
  if (!dbInstance) {
    dbInstance = new RSSDatabase(config);
  }
  return dbInstance;
}
