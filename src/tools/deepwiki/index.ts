/**
 * DeepWiki Tools for FastMCP
 * 简化设计，支持并发搜索，移除冗余的 queryType
 */

import { z } from "zod";
import { DeepWikiClient } from "./client.js";
import { DeepWikiParser } from "./parser.js";

// ============================================================================
// 工具定义 - 简化设计，移除冗余的 queryType
// ============================================================================

// 单个查询 schema
const singleSearchSchema = z.object({
  query: z
    .string()
    .describe(
      "Detailed search query for code implementations. Examples: 'authentication with JWT tokens', 'React component lifecycle', 'error handling patterns'"
    ),
  repository: z.string().describe("GitHub repository name (format: 'owner/repo', e.g., 'facebook/react', 'vercel/next.js')"),
  focus: z
    .enum(["code", "docs"])
    .optional()
    .describe("Optional search focus: 'code' for implementation examples, 'docs' for documentation and explanations")
});

// 批量查询 schema
const batchSearchSchema = z.object({
  queries: z
    .array(
      z.object({
        query: z.string().describe("Search query"),
        repository: z.string().describe("GitHub repository"),
        focus: z.enum(["code", "docs"]).optional()
      })
    )
    .min(1)
    .max(5)
    .describe("Array of search queries (max 5 for performance)"),
  concurrent: z.boolean().default(true).describe("Whether to run queries concurrently for faster results")
});

// 统一输出 schema
const searchResultSchema = z.object({
  success: z.boolean().describe("Whether the search was successful"),
  repository: z.string().describe("The repository that was searched"),
  query: z.string().describe("The search query that was used"),
  focus: z.string().optional().describe("The search focus that was used"),
  summary: z.string().describe("A summary of the search results"),
  content: z.string().describe("The formatted markdown content with code snippets"),
  combinedContent: z.string().describe("Combined content for AI processing"),
  stats: z
    .object({
      chunks: z.number().describe("Number of text chunks found"),
      references: z.number().describe("Number of file references found"),
      fileContents: z.number().describe("Number of file contents retrieved"),
      codeSnippets: z.number().describe("Number of code snippets extracted")
    })
    .describe("Statistics about the search results"),
  codeSnippets: z
    .array(
      z.object({
        repo: z.string().describe("Repository name"),
        filePath: z.string().describe("File path within the repository"),
        content: z.string().describe("The actual code content"),
        startLine: z.number().describe("Starting line number"),
        endLine: z.number().describe("Ending line number"),
        language: z.string().describe("Programming language detected")
      })
    )
    .describe("Array of extracted code snippets"),
  executionTime: z.number().describe("Execution time in milliseconds"),
  error: z.string().optional().describe("Error message if the search failed")
});

const batchSearchOutputSchema = z.object({
  results: z.array(searchResultSchema).describe("Array of search results"),
  totalExecutionTime: z.number().describe("Total execution time in milliseconds"),
  successCount: z.number().describe("Number of successful searches"),
  failureCount: z.number().describe("Number of failed searches")
});

// ============================================================================
// 辅助函数
// ============================================================================

function getLanguageFromPath(filePath: string): string {
  const ext = filePath.split(".").pop()?.toLowerCase() || "";
  const languageMap: Record<string, string> = {
    js: "javascript",
    jsx: "javascript",
    ts: "typescript",
    tsx: "typescript",
    py: "python",
    java: "java",
    cpp: "cpp",
    c: "c",
    cs: "csharp",
    php: "php",
    rb: "ruby",
    go: "go",
    rs: "rust",
    swift: "swift",
    kt: "kotlin",
    scala: "scala",
    sh: "bash",
    yml: "yaml",
    yaml: "yaml",
    json: "json",
    xml: "xml",
    html: "html",
    css: "css",
    md: "markdown"
  };
  return languageMap[ext] || ext || "text";
}

// 执行单个搜索
async function executeSingleSearch(
  query: string,
  repository: string,
  focus?: string
): Promise<z.infer<typeof searchResultSchema>> {
  const client = new DeepWikiClient();
  const parser = new DeepWikiParser();
  const startTime = Date.now();

  try {
    console.log(`🔍 Searching ${repository} for: ${query.substring(0, 50)}...`);

    const queryId = await client.createQuery(query, repository, focus);
    const response = await client.pollQuery(queryId);
    const parsed = parser.parseResponse(response);
    const markdown = parser.formatAsMarkdown(parsed, parsed.codeSnippets);

    const executionTime = Date.now() - startTime;

    return {
      success: true,
      repository,
      query,
      focus,
      summary: parser.generateSummary(parsed),
      content: markdown,
      combinedContent: parsed.combinedContent,
      stats: {
        chunks: parsed.chunks.length,
        references: parsed.references.length,
        fileContents: parsed.fileContents.length,
        codeSnippets: parsed.codeSnippets.length
      },
      codeSnippets: parsed.codeSnippets.map(snippet => ({
        repo: snippet.repo,
        filePath: snippet.filePath,
        content: snippet.content,
        startLine: snippet.startLine,
        endLine: snippet.endLine,
        language: getLanguageFromPath(snippet.filePath)
      })),
      executionTime
    };
  } catch (error) {
    const executionTime = Date.now() - startTime;
    console.error(`❌ Search failed for ${repository}:`, error);

    return {
      success: false,
      repository,
      query,
      focus,
      summary: "Search failed",
      content: "",
      combinedContent: "",
      stats: { chunks: 0, references: 0, fileContents: 0, codeSnippets: 0 },
      codeSnippets: [],
      executionTime,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}

// ============================================================================
// 工具实现 - FastMCP Tool Definitions
// ============================================================================

// 单个搜索工具
export const deepWikiSearchTool = {
  name: "deepwiki-search",
  description:
    "Search for code implementations in a GitHub repository. Simple interface with optional focus on code or documentation.",
  parameters: singleSearchSchema,
  execute: async (args: z.infer<typeof singleSearchSchema>) => {
    const { query, repository, focus } = args;
    const result = await executeSingleSearch(query, repository, focus);
    return JSON.stringify(result, null, 2);
  }
};

// 批量搜索工具
export const deepWikiBatchSearchTool = {
  name: "deepwiki-batch-search",
  description: "Search multiple repositories or queries concurrently for faster results. Maximum 5 queries per batch.",
  parameters: batchSearchSchema,
  execute: async (args: z.infer<typeof batchSearchSchema>) => {
    const { queries, concurrent } = args;
    const startTime = Date.now();

    console.log(`🚀 Starting ${concurrent ? "concurrent" : "sequential"} batch search for ${queries.length} queries`);

    let results: z.infer<typeof searchResultSchema>[];

    if (concurrent) {
      // 并发执行所有查询
      results = await Promise.all(queries.map(({ query, repository, focus }) => executeSingleSearch(query, repository, focus)));
    } else {
      // 顺序执行查询
      results = [];
      for (const { query, repository, focus } of queries) {
        const result = await executeSingleSearch(query, repository, focus);
        results.push(result);
      }
    }

    const totalExecutionTime = Date.now() - startTime;
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;

    console.log(`✅ Batch search completed: ${successCount} success, ${failureCount} failures in ${totalExecutionTime}ms`);

    const batchResult = {
      results,
      totalExecutionTime,
      successCount,
      failureCount
    };

    return JSON.stringify(batchResult, null, 2);
  }
};
