/**
 * MCP Domain Tools Test
 * 测试MCP工具接口
 */

import { domainSearchTool, domainBatchSearchTool, domainDetailsTool } from './index.js';

/**
 * 测试单个域名搜索工具
 */
async function testDomainSearchTool() {
  console.log('\n🧪 Testing domain-search tool...');
  
  try {
    // 测试可用域名搜索
    console.log('\n📋 Testing available domain search:');
    const availableResult = await domainSearchTool.execute({
      keyword: 'mytestapp',
      suffixes: ['.com', '.io', '.app'],
      mode: 'available'
    });
    console.log('Available domains result length:', availableResult.length);
    
    // 测试已注册域名搜索
    console.log('\n📋 Testing registered domain search:');
    const registeredResult = await domainSearchTool.execute({
      keyword: 'google',
      suffixes: ['.com', '.org'],
      mode: 'registered'
    });
    console.log('Registered domains result length:', registeredResult.length);
    
    console.log('✅ Domain search tool test passed');
  } catch (error) {
    console.error('❌ Domain search tool test failed:', error);
  }
}

/**
 * 测试批量域名搜索工具
 */
async function testDomainBatchSearchTool() {
  console.log('\n🧪 Testing domain-batch-search tool...');
  
  try {
    // 测试并发批量搜索
    console.log('\n📋 Testing concurrent batch search:');
    const concurrentResult = await domainBatchSearchTool.execute({
      keywords: ['testapp1', 'testapp2'],
      suffixes: ['.com', '.io'],
      mode: 'available',
      concurrent: true
    });
    console.log('Concurrent batch result length:', concurrentResult.length);
    
    // 测试顺序批量搜索
    console.log('\n📋 Testing sequential batch search:');
    const sequentialResult = await domainBatchSearchTool.execute({
      keywords: ['example', 'demo'],
      suffixes: ['.com'],
      mode: 'registered',
      concurrent: false
    });
    console.log('Sequential batch result length:', sequentialResult.length);
    
    console.log('✅ Domain batch search tool test passed');
  } catch (error) {
    console.error('❌ Domain batch search tool test failed:', error);
  }
}

/**
 * 测试域名详情工具
 */
async function testDomainDetailsTool() {
  console.log('\n🧪 Testing domain-details tool...');
  
  try {
    const result = await domainDetailsTool.execute({
      domains: ['google.com', 'nonexistentdomain999.com']
    });
    console.log('Domain details result length:', result.length);
    
    console.log('✅ Domain details tool test passed');
  } catch (error) {
    console.error('❌ Domain details tool test failed:', error);
  }
}

/**
 * 测试工具参数验证
 */
async function testParameterValidation() {
  console.log('\n🧪 Testing parameter validation...');
  
  try {
    // 测试空关键词
    try {
      await domainSearchTool.execute({
        keyword: '',
        mode: 'available'
      });
      console.log('❌ Should have failed for empty keyword');
    } catch (error) {
      console.log('✅ Correctly rejected empty keyword');
    }
    
    // 测试过多关键词
    try {
      await domainBatchSearchTool.execute({
        keywords: ['a', 'b', 'c', 'd', 'e', 'f'], // 6个关键词，超过限制
        mode: 'available'
      });
      console.log('❌ Should have failed for too many keywords');
    } catch (error) {
      console.log('✅ Correctly rejected too many keywords');
    }
    
    // 测试过多域名
    try {
      await domainDetailsTool.execute({
        domains: Array(11).fill('test.com') // 11个域名，超过限制
      });
      console.log('❌ Should have failed for too many domains');
    } catch (error) {
      console.log('✅ Correctly rejected too many domains');
    }
    
    console.log('✅ Parameter validation test passed');
  } catch (error) {
    console.error('❌ Parameter validation test failed:', error);
  }
}

/**
 * 运行所有MCP工具测试
 */
async function runAllMCPTests() {
  console.log('🚀 Starting MCP Domain Tools Test Suite');
  console.log('=========================================');
  
  const startTime = Date.now();
  
  await testDomainSearchTool();
  await testDomainBatchSearchTool();
  await testDomainDetailsTool();
  await testParameterValidation();
  
  const endTime = Date.now();
  const duration = (endTime - startTime) / 1000;
  
  console.log('\n=========================================');
  console.log(`🎉 MCP test suite completed in ${duration.toFixed(2)} seconds`);
  console.log('=========================================');
}

// 运行测试
if (import.meta.main) {
  runAllMCPTests().catch(console.error);
}

export {
  testDomainSearchTool,
  testDomainBatchSearchTool,
  testDomainDetailsTool,
  testParameterValidation,
  runAllMCPTests
};
