/**
 * Domain Tools for FastMCP
 * 专注于高效域名搜索，支持关键词+后缀组合和两种搜索模式
 * 简化接口，内置最佳实践，支持批量并发搜索
 */

import { z } from "zod";
import {
    searchDomainsByKeyword,
    batchSearchDomains,
    getDomainDetails,
    DomainSearchOptions,
    BatchDomainSearchOptions
} from "./search.js";
import { DomainClient } from "./client.js";

// ============================================================================
// Tool Definitions - Simple Design
// ============================================================================

// Single domain search schema
const singleDomainSearchSchema = z.object({
    keyword: z.string().min(1).describe("Domain keyword to search (e.g., 'myapp', 'startup', 'tech')"),
    suffixes: z
        .array(z.string())
        .optional()
        .describe(`Domain suffixes to check. Default: ${DomainClient.COMMON_SUFFIXES.join(', ')}. Examples: ['.com', '.io', '.app']`),
    mode: z
        .enum(["available", "registered"])
        .default("available")
        .describe("Search mode: 'available' for unregistered domains, 'registered' for registered domains"),
    timeout: z
        .number()
        .min(5000)
        .max(60000)
        .default(30000)
        .optional()
        .describe("Request timeout in milliseconds (5000-60000, default: 30000)")
});

// Batch domain search schema
const batchDomainSearchSchema = z.object({
    keywords: z
        .array(z.string().min(1))
        .min(1)
        .max(5)
        .describe("Array of domain keywords to search (max 5 for performance). Examples: ['myapp', 'startup', 'tech']"),
    suffixes: z
        .array(z.string())
        .optional()
        .describe(`Domain suffixes to check for each keyword. Default: ${DomainClient.COMMON_SUFFIXES.join(', ')}`),
    mode: z
        .enum(["available", "registered"])
        .default("available")
        .describe("Search mode: 'available' for unregistered domains, 'registered' for registered domains"),
    concurrent: z
        .boolean()
        .default(true)
        .describe("Whether to run searches concurrently for faster results"),
    timeout: z
        .number()
        .min(5000)
        .max(60000)
        .default(30000)
        .optional()
        .describe("Request timeout in milliseconds per search (5000-60000, default: 30000)")
});

// Domain details schema
const domainDetailsSchema = z.object({
    domains: z
        .array(z.string().min(1))
        .min(1)
        .max(10)
        .describe("Array of specific domains to check (max 10). Examples: ['google.com', 'github.io', 'example.org']"),
    timeout: z
        .number()
        .min(5000)
        .max(60000)
        .default(30000)
        .optional()
        .describe("Request timeout in milliseconds (5000-60000, default: 30000)")
});

// ============================================================================
// Helper Functions
// ============================================================================

// Format batch search results for output
function formatBatchResults(results: any[]): string {
    let output = `# 🔍 批量域名搜索结果\n\n`;
    output += `## 📊 总体统计\n\n`;

    const totalKeywords = results.length;
    const totalDomains = results.reduce((sum, r) => sum + r.totalChecked, 0);
    const totalAvailable = results.reduce((sum, r) => sum + r.availableCount, 0);
    const totalRegistered = results.reduce((sum, r) => sum + r.registeredCount, 0);

    output += `- **搜索关键词**: ${totalKeywords}\n`;
    output += `- **检查域名总数**: ${totalDomains}\n`;
    output += `- **可用域名**: ${totalAvailable}\n`;
    output += `- **已注册域名**: ${totalRegistered}\n\n`;

    // Add individual results
    results.forEach((result, index) => {
        output += `## 关键词 ${index + 1} 搜索结果\n\n`;
        output += result.markdown + '\n';
    });

    return output;
}

// ============================================================================
// Tool Implementations
// ============================================================================

// Single domain search tool
export const domainSearchTool = {
    name: "domain-search",
    description:
        "Search for domain availability by keyword. Automatically checks common domain suffixes (.com, .io, .app, etc.) and provides detailed registration information. Supports both available and registered domain searches.",
    parameters: singleDomainSearchSchema,
    execute: async (args: z.infer<typeof singleDomainSearchSchema>) => {
        const { keyword, suffixes, mode, timeout } = args;

        console.log(`🔍 Domain search: ${keyword} (${mode} mode)`);

        const options: DomainSearchOptions = {
            keyword,
            suffixes,
            mode,
            timeout
        };

        const result = await searchDomainsByKeyword(options);

        return result.markdown || "No results found";
    }
};

// Batch domain search tool
export const domainBatchSearchTool = {
    name: "domain-batch-search",
    description:
        "Search multiple domain keywords concurrently for efficient bulk domain checking. Maximum 5 keywords per batch for optimal performance. Perfect for comparing domain availability across multiple project names.",
    parameters: batchDomainSearchSchema,
    execute: async (args: z.infer<typeof batchDomainSearchSchema>) => {
        const { keywords, suffixes, mode, concurrent, timeout } = args;

        console.log(`🚀 Batch domain search: ${keywords.length} keywords (${mode} mode, ${concurrent ? 'concurrent' : 'sequential'})`);

        const options: BatchDomainSearchOptions = {
            keywords,
            suffixes,
            mode,
            concurrent,
            timeout
        };

        const results = await batchSearchDomains(options);

        return formatBatchResults(results);
    }
};

// Domain details tool
export const domainDetailsTool = {
    name: "domain-details",
    description:
        "Get detailed information for specific domains including registration dates, expiration dates, pricing, and availability status. Perfect for checking exact domains you're interested in.",
    parameters: domainDetailsSchema,
    execute: async (args: z.infer<typeof domainDetailsSchema>) => {
        const { domains } = args;

        console.log(`🔍 Domain details: ${domains.join(', ')}`);

        const result = await getDomainDetails(domains);

        return result.markdown || "No details found";
    }
};