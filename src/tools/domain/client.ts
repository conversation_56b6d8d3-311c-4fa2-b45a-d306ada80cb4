/**
 * Domain Search Client for FastMCP
 * 处理域名查询API请求和SSE响应解析
 */

export interface DomainSearchParams {
  domains: string[];
  returnDates?: boolean;
  returnPrices?: boolean;
  timeout?: number;
}

export interface DomainCheckResult {
  domain: string;
  existed: 'yes' | 'no' | 'unknown';
  registered?: string;
  expires?: string;
  duration?: number;
  market?: string;
  reserved?: boolean;
  premium?: boolean;
  price?: string;
  eventType: 'shallow-checked' | 'whois-cache-checked' | 'whois-realtime-checked' | 'price-checked';
}

export interface DomainSearchResponse {
  results: DomainCheckResult[];
  totalDuration: number;
  success: boolean;
  error?: string;
}

export class DomainClient {
  private readonly baseUrl = 'https://instant.who.sb/api/v1/check';
  private readonly defaultTimeout = 30000; // 30秒超时

  /**
   * 搜索多个域名的注册状态
   */
  async searchDomains(params: DomainSearchParams): Promise<DomainSearchResponse> {
    const { domains, returnDates = true, returnPrices = true, timeout = this.defaultTimeout } = params;

    if (!domains || domains.length === 0) {
      throw new Error('Domains array cannot be empty');
    }

    // 构建查询URL
    const domainQuery = domains.join(',');
    const url = new URL(this.baseUrl);
    url.searchParams.set('domain', domainQuery);
    url.searchParams.set('sse', 'true');
    if (returnDates) url.searchParams.set('return_dates', 'true');
    if (returnPrices) url.searchParams.set('return-prices', 'true');

    try {
      console.log(`🔍 Searching domains: ${domains.join(', ')}`);
      
      const response = await this.fetchWithTimeout(url.toString(), {
        method: 'GET',
        headers: {
          'accept': '*/*',
          'accept-language': 'zh-CN,zh;q=0.9',
          'sec-fetch-dest': 'empty',
          'sec-fetch-mode': 'cors',
          'sec-fetch-site': 'cross-site'
        }
      }, timeout);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const results = await this.parseSSEResponse(response);
      
      return {
        results,
        totalDuration: this.calculateTotalDuration(results),
        success: true
      };
    } catch (error) {
      console.error('❌ Domain search failed:', error);
      return {
        results: [],
        totalDuration: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * 带超时的fetch请求
   */
  private async fetchWithTimeout(url: string, options: RequestInit, timeout: number): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error(`Request timeout after ${timeout}ms`);
      }
      throw error;
    }
  }

  /**
   * 解析SSE响应流
   */
  private async parseSSEResponse(response: Response): Promise<DomainCheckResult[]> {
    const results: DomainCheckResult[] = [];
    const reader = response.body?.getReader();
    
    if (!reader) {
      throw new Error('Response body is not readable');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;
        
        buffer += decoder.decode(value, { stream: true });
        
        // 处理完整的SSE事件
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // 保留不完整的行
        
        let currentEvent: any = {};
        
        for (const line of lines) {
          if (line.startsWith('event: ')) {
            currentEvent.type = line.substring(7);
          } else if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.substring(6));
              currentEvent.data = data;
            } catch (e) {
              // 忽略无法解析的数据行
              continue;
            }
          } else if (line.startsWith('id: ')) {
            currentEvent.id = line.substring(4);
          } else if (line === '') {
            // 空行表示事件结束
            if (currentEvent.type && currentEvent.data) {
              const result = this.parseEventData(currentEvent);
              if (result) {
                results.push(result);
              }
            }
            currentEvent = {};
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    return results;
  }

  /**
   * 解析单个SSE事件数据
   */
  private parseEventData(event: any): DomainCheckResult | null {
    const { type, data } = event;
    
    if (type === '[DONE]') {
      return null; // 结束事件，不返回结果
    }

    if (!data || !data.domain) {
      return null;
    }

    const result: DomainCheckResult = {
      domain: data.domain,
      existed: data.meta?.existed || 'unknown',
      eventType: type as any
    };

    // 根据事件类型解析不同的字段
    switch (type) {
      case 'shallow-checked':
        result.duration = data.meta?.duration;
        result.market = data.meta?.market;
        break;
        
      case 'whois-cache-checked':
      case 'whois-realtime-checked':
        result.registered = data.meta?.registered;
        result.expires = data.meta?.expires;
        result.reserved = data.meta?.reserved;
        result.premium = data.meta?.premium;
        break;
        
      case 'price-checked':
        result.premium = data.meta?.premium;
        result.price = data.meta?.price;
        break;
    }

    return result;
  }

  /**
   * 计算总耗时
   */
  private calculateTotalDuration(results: DomainCheckResult[]): number {
    const durations = results
      .map(r => r.duration)
      .filter((d): d is number => typeof d === 'number');
    
    return durations.length > 0 ? Math.max(...durations) : 0;
  }

  /**
   * 生成域名组合
   */
  static generateDomainCombinations(keyword: string, suffixes: string[]): string[] {
    return suffixes.map(suffix => {
      const cleanSuffix = suffix.startsWith('.') ? suffix : `.${suffix}`;
      return `${keyword}${cleanSuffix}`;
    });
  }

  /**
   * 常用域名后缀
   */
  static readonly COMMON_SUFFIXES = [
    '.com', '.net', '.org', '.io', '.ai', '.app', '.dev', 
    '.co', '.me', '.xyz', '.top', '.pro', '.run', '.info', '.cn'
  ];
}
