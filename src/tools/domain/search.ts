/**
 * Domain Search Functions for FastMCP
 * 提供高级域名搜索接口，支持关键词+后缀组合和两种过滤模式
 */

import { DomainClient, DomainSearchParams } from './client.js';
import { DomainParser, SearchMode, DomainSearchSummary } from './parser.js';

export interface DomainSearchOptions {
    keyword: string;
    suffixes?: string[];
    mode?: SearchMode;
    timeout?: number;
    returnDates?: boolean;
    returnPrices?: boolean;
}

export interface BatchDomainSearchOptions {
    keywords: string[];
    suffixes?: string[];
    mode?: SearchMode;
    timeout?: number;
    concurrent?: boolean;
    returnDates?: boolean;
    returnPrices?: boolean;
}

/**
 * 搜索单个关键词的域名
 */
export async function searchDomainsByKeyword(options: DomainSearchOptions): Promise<DomainSearchSummary> {
    const {
        keyword,
        suffixes = DomainClient.COMMON_SUFFIXES,
        mode = 'available',
        timeout = 30000,
        returnDates = true,
        returnPrices = true
    } = options;

    // 生成域名组合
    const domains = DomainClient.generateDomainCombinations(keyword, suffixes);

    console.log(`🔍 Searching domains for keyword: ${keyword}`);
    console.log(`📋 Checking ${domains.length} domain combinations`);

    // 创建客户端和解析器
    const client = new DomainClient();
    const parser = new DomainParser();

    // 执行搜索
    const searchParams: DomainSearchParams = {
        domains,
        returnDates,
        returnPrices,
        timeout
    };

    const response = await client.searchDomains(searchParams);

    // 解析结果
    const summary = parser.parseResponse(response, mode);

    console.log(`✅ Search completed: ${summary.results.length} results found`);

    return summary;
}

/**
 * 批量搜索多个关键词的域名
 */
export async function batchSearchDomains(options: BatchDomainSearchOptions): Promise<DomainSearchSummary[]> {
    const {
        keywords,
        suffixes = DomainClient.COMMON_SUFFIXES,
        mode = 'available',
        timeout = 30000,
        concurrent = true,
        returnDates = true,
        returnPrices = true
    } = options;

    console.log(`🚀 Starting ${concurrent ? 'concurrent' : 'sequential'} batch search for ${keywords.length} keywords`);

    const searchOptions: DomainSearchOptions = {
        keyword: '', // 将在循环中设置
        suffixes,
        mode,
        timeout,
        returnDates,
        returnPrices
    };

    let results: DomainSearchSummary[];

    if (concurrent) {
        // 并发执行所有搜索
        results = await Promise.all(
            keywords.map(keyword =>
                searchDomainsByKeyword({ ...searchOptions, keyword })
            )
        );
    } else {
        // 顺序执行搜索
        results = [];
        for (const keyword of keywords) {
            const result = await searchDomainsByKeyword({ ...searchOptions, keyword });
            results.push(result);
        }
    }

    console.log(`✅ Batch search completed: ${results.length} keyword searches finished`);

    return results;
}

/**
 * 搜索可用域名（快捷方法）
 */
export async function searchAvailableDomains(keyword: string, suffixes?: string[]): Promise<DomainSearchSummary> {
    return searchDomainsByKeyword({
        keyword,
        suffixes,
        mode: 'available'
    });
}

/**
 * 搜索已注册域名（快捷方法）
 */
export async function searchRegisteredDomains(keyword: string, suffixes?: string[]): Promise<DomainSearchSummary> {
    return searchDomainsByKeyword({
        keyword,
        suffixes,
        mode: 'registered'
    });
}

/**
 * 获取域名详细信息
 */
export async function getDomainDetails(domains: string[]): Promise<DomainSearchSummary> {
    console.log(`🔍 Getting details for domains: ${domains.join(', ')}`);

    const client = new DomainClient();
    const parser = new DomainParser();

    const searchParams: DomainSearchParams = {
        domains,
        returnDates: true,
        returnPrices: true,
        timeout: 30000
    };

    const response = await client.searchDomains(searchParams);
    const summary = parser.parseResponse(response, 'available'); // 显示所有结果

    console.log(`✅ Domain details retrieved: ${summary.results.length} domains processed`);

    return summary;
}