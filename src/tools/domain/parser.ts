/**
 * Domain Search Parser for FastMCP
 * 解析域名搜索结果并提供格式化输出
 */

import { DomainCheckResult, DomainSearchResponse } from './client.js';

export type SearchMode = 'registered' | 'available';

export interface ParsedDomainResult {
  domain: string;
  status: 'registered' | 'available' | 'unknown';
  registered?: string;
  expires?: string;
  market?: string;
  price?: string;
  premium?: boolean;
  reserved?: boolean;
  duration?: number;
}

export interface DomainSearchSummary {
  totalChecked: number;
  registeredCount: number;
  availableCount: number;
  unknownCount: number;
  premiumCount: number;
  totalDuration: number;
  mode: SearchMode;
  results: ParsedDomainResult[];
  markdown: string;
  summary: string;
}

export class DomainParser {
  /**
   * 解析域名搜索响应
   */
  parseResponse(response: DomainSearchResponse, mode: SearchMode = 'available'): DomainSearchSummary {
    if (!response.success) {
      return this.createErrorSummary(response.error || 'Search failed', mode);
    }

    // 合并同一域名的多个检查结果
    const mergedResults = this.mergeResults(response.results);
    
    // 解析为标准格式
    const parsedResults = mergedResults.map(result => this.parseResult(result));
    
    // 根据模式过滤结果
    const filteredResults = this.filterByMode(parsedResults, mode);
    
    // 生成统计信息
    const stats = this.calculateStats(parsedResults);
    
    // 生成格式化输出
    const markdown = this.generateMarkdown(filteredResults, stats, mode);
    const summary = this.generateSummary(stats, mode);

    return {
      ...stats,
      mode,
      results: filteredResults,
      markdown,
      summary,
      totalDuration: response.totalDuration
    };
  }

  /**
   * 合并同一域名的多个检查结果
   */
  private mergeResults(results: DomainCheckResult[]): DomainCheckResult[] {
    const domainMap = new Map<string, DomainCheckResult>();

    for (const result of results) {
      const existing = domainMap.get(result.domain);
      
      if (!existing) {
        domainMap.set(result.domain, { ...result });
      } else {
        // 合并信息，优先使用更详细的数据
        const merged = { ...existing };
        
        // 更新状态（优先实时检查结果）
        if (result.eventType === 'whois-realtime-checked' || 
            (result.eventType === 'whois-cache-checked' && existing.eventType === 'shallow-checked')) {
          merged.existed = result.existed;
        }
        
        // 合并其他字段
        if (result.registered) merged.registered = result.registered;
        if (result.expires) merged.expires = result.expires;
        if (result.market) merged.market = result.market;
        if (result.price) merged.price = result.price;
        if (result.premium !== undefined) merged.premium = result.premium;
        if (result.reserved !== undefined) merged.reserved = result.reserved;
        if (result.duration) merged.duration = Math.max(merged.duration || 0, result.duration);
        
        domainMap.set(result.domain, merged);
      }
    }

    return Array.from(domainMap.values());
  }

  /**
   * 解析单个域名结果
   */
  private parseResult(result: DomainCheckResult): ParsedDomainResult {
    let status: 'registered' | 'available' | 'unknown';
    
    switch (result.existed) {
      case 'yes':
        status = 'registered';
        break;
      case 'no':
        status = 'available';
        break;
      default:
        status = 'unknown';
    }

    return {
      domain: result.domain,
      status,
      registered: result.registered,
      expires: result.expires,
      market: result.market,
      price: result.price,
      premium: result.premium,
      reserved: result.reserved,
      duration: result.duration
    };
  }

  /**
   * 根据模式过滤结果
   */
  private filterByMode(results: ParsedDomainResult[], mode: SearchMode): ParsedDomainResult[] {
    switch (mode) {
      case 'registered':
        return results.filter(r => r.status === 'registered');
      case 'available':
        return results.filter(r => r.status === 'available');
      default:
        return results;
    }
  }

  /**
   * 计算统计信息
   */
  private calculateStats(results: ParsedDomainResult[]) {
    const registeredCount = results.filter(r => r.status === 'registered').length;
    const availableCount = results.filter(r => r.status === 'available').length;
    const unknownCount = results.filter(r => r.status === 'unknown').length;
    const premiumCount = results.filter(r => r.premium === true).length;

    return {
      totalChecked: results.length,
      registeredCount,
      availableCount,
      unknownCount,
      premiumCount
    };
  }

  /**
   * 生成Markdown格式输出
   */
  private generateMarkdown(results: ParsedDomainResult[], stats: any, mode: SearchMode): string {
    const modeText = mode === 'registered' ? '已注册域名' : '可用域名';
    
    let markdown = `# 🔍 域名搜索结果 - ${modeText}\n\n`;
    
    // 统计信息
    markdown += `## 📊 统计信息\n\n`;
    markdown += `- **总检查数量**: ${stats.totalChecked}\n`;
    markdown += `- **已注册**: ${stats.registeredCount}\n`;
    markdown += `- **可用**: ${stats.availableCount}\n`;
    markdown += `- **未知**: ${stats.unknownCount}\n`;
    if (stats.premiumCount > 0) {
      markdown += `- **高级域名**: ${stats.premiumCount}\n`;
    }
    markdown += `\n`;

    if (results.length === 0) {
      markdown += `## ❌ 无结果\n\n没有找到符合条件的${modeText}。\n`;
      return markdown;
    }

    // 结果列表
    markdown += `## 📋 ${modeText}列表\n\n`;
    
    for (const result of results) {
      markdown += `### ${result.domain}\n\n`;
      markdown += `- **状态**: ${this.getStatusEmoji(result.status)} ${this.getStatusText(result.status)}\n`;
      
      if (result.registered) {
        markdown += `- **注册时间**: ${this.formatDate(result.registered)}\n`;
      }
      
      if (result.expires) {
        markdown += `- **过期时间**: ${this.formatDate(result.expires)}\n`;
      }
      
      if (result.price) {
        markdown += `- **价格**: ${result.price}\n`;
      }
      
      if (result.premium) {
        markdown += `- **类型**: 🌟 高级域名\n`;
      }
      
      if (result.reserved) {
        markdown += `- **保留状态**: 🔒 保留域名\n`;
      }
      
      if (result.market) {
        markdown += `- **交易市场**: [查看](${result.market})\n`;
      }
      
      if (result.duration) {
        markdown += `- **查询耗时**: ${result.duration}ms\n`;
      }
      
      markdown += `\n`;
    }

    return markdown;
  }

  /**
   * 生成摘要信息
   */
  private generateSummary(stats: any, mode: SearchMode): string {
    const modeText = mode === 'registered' ? '已注册域名' : '可用域名';
    const count = mode === 'registered' ? stats.registeredCount : stats.availableCount;
    
    return `找到 ${count} 个${modeText}，总共检查了 ${stats.totalChecked} 个域名。`;
  }

  /**
   * 创建错误摘要
   */
  private createErrorSummary(error: string, mode: SearchMode): DomainSearchSummary {
    return {
      totalChecked: 0,
      registeredCount: 0,
      availableCount: 0,
      unknownCount: 0,
      premiumCount: 0,
      totalDuration: 0,
      mode,
      results: [],
      markdown: `# ❌ 域名搜索失败\n\n**错误信息**: ${error}\n`,
      summary: `域名搜索失败: ${error}`
    };
  }

  /**
   * 获取状态表情符号
   */
  private getStatusEmoji(status: string): string {
    switch (status) {
      case 'registered': return '✅';
      case 'available': return '🟢';
      case 'unknown': return '❓';
      default: return '❓';
    }
  }

  /**
   * 获取状态文本
   */
  private getStatusText(status: string): string {
    switch (status) {
      case 'registered': return '已注册';
      case 'available': return '可用';
      case 'unknown': return '未知';
      default: return '未知';
    }
  }

  /**
   * 格式化日期
   */
  private formatDate(dateString: string): string {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch {
      return dateString;
    }
  }
}
