/**
 * Domain Search Demo
 * 演示域名搜索工具的功能
 */

import { domainSearchTool, domainBatchSearchTool, domainDetailsTool } from './index.js';

async function demo() {
  console.log('🚀 域名搜索工具演示');
  console.log('===================');

  // 演示1: 搜索可用域名
  console.log('\n📋 演示1: 搜索可用域名');
  console.log('关键词: "myawesomeapp"');
  console.log('后缀: .com, .io, .app');
  
  const availableResult = await domainSearchTool.execute({
    keyword: 'myawesomeapp',
    suffixes: ['.com', '.io', '.app'],
    mode: 'available'
  });
  
  console.log('\n结果:');
  console.log(availableResult);

  // 演示2: 批量搜索
  console.log('\n📋 演示2: 批量搜索多个关键词');
  console.log('关键词: ["startup", "innovation"]');
  console.log('后缀: .com, .io');
  
  const batchResult = await domainBatchSearchTool.execute({
    keywords: ['startup', 'innovation'],
    suffixes: ['.com', '.io'],
    mode: 'available',
    concurrent: true
  });
  
  console.log('\n结果:');
  console.log(batchResult);

  // 演示3: 查询特定域名详情
  console.log('\n📋 演示3: 查询特定域名详情');
  console.log('域名: google.com, github.io');
  
  const detailsResult = await domainDetailsTool.execute({
    domains: ['google.com', 'github.io']
  });
  
  console.log('\n结果:');
  console.log(detailsResult);
}

if (import.meta.main) {
  demo().catch(console.error);
}
