# Domain Search Tools for FastMCP

高效的域名搜索工具，支持关键词+后缀组合和两种搜索模式（可用域名/已注册域名）。

## 功能特性

- 🔍 **智能域名搜索**: 输入关键词自动检查多个域名后缀
- 🚀 **批量并发搜索**: 支持多个关键词同时搜索，提高效率
- 📊 **两种搜索模式**: 可用域名搜索和已注册域名搜索
- 💰 **价格信息**: 显示可用域名的注册价格
- 📅 **注册信息**: 显示已注册域名的注册和过期时间
- 🛡️ **错误处理**: 完善的超时和错误处理机制
- 📝 **格式化输出**: Markdown格式的美观输出

## 文件结构

```
src/tools/domain/
├── client.ts          # DomainClient类，处理API请求和SSE解析
├── parser.ts          # DomainParser类，解析响应数据和格式化输出
├── search.ts          # 高级搜索接口和批量搜索功能
├── index.ts           # MCP工具导出
├── test.ts            # 功能测试套件
├── mcp-test.ts        # MCP工具测试
├── demo.ts            # 功能演示
└── README.md          # 本文档
```

## 快速开始

### 1. 运行测试

```bash
# 运行核心功能测试
bun run src/tools/domain/test.ts

# 运行MCP工具测试
bun run src/tools/domain/mcp-test.ts

# 运行功能演示
bun run src/tools/domain/demo.ts
```

### 2. 使用核心API

```typescript
import { searchAvailableDomains, searchRegisteredDomains, batchSearchDomains } from './search.js';

// 搜索可用域名
const available = await searchAvailableDomains('myapp', ['.com', '.io', '.app']);

// 搜索已注册域名
const registered = await searchRegisteredDomains('google', ['.com', '.org']);

// 批量搜索
const batch = await batchSearchDomains({
  keywords: ['startup', 'innovation'],
  suffixes: ['.com', '.io'],
  mode: 'available',
  concurrent: true
});
```

### 3. 使用MCP工具

```typescript
import { domainSearchTool, domainBatchSearchTool, domainDetailsTool } from './index.js';

// 单个域名搜索
const result1 = await domainSearchTool.execute({
  keyword: 'myapp',
  suffixes: ['.com', '.io'],
  mode: 'available'
});

// 批量域名搜索
const result2 = await domainBatchSearchTool.execute({
  keywords: ['app1', 'app2'],
  suffixes: ['.com', '.io'],
  mode: 'available',
  concurrent: true
});

// 域名详情查询
const result3 = await domainDetailsTool.execute({
  domains: ['google.com', 'github.io']
});
```

## MCP工具说明

### 1. domain-search

搜索单个关键词的域名可用性。

**参数:**
- `keyword` (string): 域名关键词
- `suffixes` (string[], 可选): 域名后缀列表
- `mode` ('available' | 'registered', 默认: 'available'): 搜索模式
- `timeout` (number, 可选): 超时时间（毫秒）

### 2. domain-batch-search

批量搜索多个关键词的域名。

**参数:**
- `keywords` (string[]): 关键词列表（最多5个）
- `suffixes` (string[], 可选): 域名后缀列表
- `mode` ('available' | 'registered', 默认: 'available'): 搜索模式
- `concurrent` (boolean, 默认: true): 是否并发执行
- `timeout` (number, 可选): 超时时间（毫秒）

### 3. domain-details

查询特定域名的详细信息。

**参数:**
- `domains` (string[]): 域名列表（最多10个）
- `timeout` (number, 可选): 超时时间（毫秒）

## 常用域名后缀

工具默认支持以下域名后缀：
- `.com`, `.net`, `.org` - 传统后缀
- `.io`, `.ai`, `.app`, `.dev` - 技术类后缀
- `.co`, `.me`, `.xyz` - 现代后缀
- `.top`, `.pro`, `.run`, `.info` - 其他后缀
- `.cn` - 中国后缀

## API说明

### 搜索模式

1. **available模式**: 返回未注册的可用域名
   - 显示注册价格
   - 标记高级域名
   - 显示保留状态

2. **registered模式**: 返回已注册的域名
   - 显示注册时间
   - 显示过期时间
   - 显示交易市场链接

### 输出格式

所有工具都返回Markdown格式的结果，包含：
- 📊 统计信息
- 📋 域名列表
- 💰 价格信息（可用域名）
- 📅 注册信息（已注册域名）

## 性能优化

- 使用并发请求提高批量搜索速度
- 智能超时处理避免长时间等待
- SSE流式解析提高响应速度
- 结果去重和合并优化

## 错误处理

- 网络超时自动重试
- 无效域名格式检查
- API限制和错误响应处理
- 空结果友好提示

## 注意事项

1. API有请求频率限制，建议合理使用
2. 批量搜索建议不超过5个关键词
3. 域名详情查询建议不超过10个域名
4. 超时时间建议设置在5-60秒之间
