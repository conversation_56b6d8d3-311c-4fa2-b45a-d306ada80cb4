/**
 * Domain Search Test Suite
 * 测试域名搜索功能的各种场景
 */

import {
  searchDomainsByKeyword,
  batchSearchDomains,
  searchAvailableDomains,
  searchRegisteredDomains,
  getDomainDetails
} from './search.js';

/**
 * 测试单个关键词搜索
 */
async function testSingleKeywordSearch() {
  console.log('\n🧪 Testing single keyword search...');

  try {
    // 测试搜索可用域名
    console.log('\n📋 Testing available domains search:');
    const availableResult = await searchAvailableDomains('testdomain123', ['.com', '.net', '.org', '.io']);
    console.log(`Found ${availableResult.results.length} available domains`);
    console.log('Sample result:', availableResult.results[0]);

    // 测试搜索已注册域名
    console.log('\n📋 Testing registered domains search:');
    const registeredResult = await searchRegisteredDomains('google', ['.com', '.net', '.org']);
    console.log(`Found ${registeredResult.results.length} registered domains`);
    console.log('Sample result:', registeredResult.results[0]);

    console.log('✅ Single keyword search test passed');
  } catch (error) {
    console.error('❌ Single keyword search test failed:', error);
  }
}

/**
 * 测试批量关键词搜索
 */
async function testBatchKeywordSearch() {
  console.log('\n🧪 Testing batch keyword search...');

  try {
    // 测试并发搜索
    console.log('\n📋 Testing concurrent batch search:');
    const concurrentResults = await batchSearchDomains({
      keywords: ['testapp', 'myproject'],
      suffixes: ['.com', '.io', '.app'],
      mode: 'available',
      concurrent: true
    });

    console.log(`Concurrent search completed: ${concurrentResults.length} keyword results`);
    concurrentResults.forEach((result, index) => {
      console.log(`Keyword ${index + 1}: ${result.results.length} available domains`);
    });

    // 测试顺序搜索
    console.log('\n📋 Testing sequential batch search:');
    const sequentialResults = await batchSearchDomains({
      keywords: ['example', 'demo'],
      suffixes: ['.com', '.net'],
      mode: 'registered',
      concurrent: false
    });

    console.log(`Sequential search completed: ${sequentialResults.length} keyword results`);
    sequentialResults.forEach((result, index) => {
      console.log(`Keyword ${index + 1}: ${result.results.length} registered domains`);
    });

    console.log('✅ Batch keyword search test passed');
  } catch (error) {
    console.error('❌ Batch keyword search test failed:', error);
  }
}

/**
 * 测试域名详情查询
 */
async function testDomainDetails() {
  console.log('\n🧪 Testing domain details query...');

  try {
    const domains = ['google.com', 'github.io', 'nonexistentdomain123.com'];
    const details = await getDomainDetails(domains);

    console.log(`Domain details retrieved for ${details.results.length} domains`);
    details.results.forEach(result => {
      console.log(`${result.domain}: ${result.status}`);
      if (result.registered) {
        console.log(`  Registered: ${result.registered}`);
      }
      if (result.expires) {
        console.log(`  Expires: ${result.expires}`);
      }
    });

    console.log('✅ Domain details test passed');
  } catch (error) {
    console.error('❌ Domain details test failed:', error);
  }
}

/**
 * 测试错误处理
 */
async function testErrorHandling() {
  console.log('\n🧪 Testing error handling...');

  try {
    // 测试空关键词
    console.log('\n📋 Testing empty keyword:');
    try {
      await searchDomainsByKeyword({ keyword: '' });
      console.log('❌ Should have thrown error for empty keyword');
    } catch (error) {
      console.log('✅ Correctly handled empty keyword error');
    }

    // 测试超时设置
    console.log('\n📋 Testing timeout:');
    const timeoutResult = await searchDomainsByKeyword({
      keyword: 'timeout-test',
      suffixes: ['.com'],
      timeout: 1000 // 1秒超时
    });

    if (timeoutResult.results.length >= 0) {
      console.log('✅ Timeout handling works (may succeed or fail gracefully)');
    }

    console.log('✅ Error handling test passed');
  } catch (error) {
    console.error('❌ Error handling test failed:', error);
  }
}

/**
 * 测试输出格式
 */
async function testOutputFormat() {
  console.log('\n🧪 Testing output format...');

  try {
    const result = await searchDomainsByKeyword({
      keyword: 'format-test',
      suffixes: ['.com', '.io'],
      mode: 'available'
    });

    console.log('\n📋 Testing markdown output:');
    console.log('Markdown length:', result.markdown.length);
    console.log('Summary:', result.summary);

    console.log('\n📋 Testing result structure:');
    console.log('Total checked:', result.totalChecked);
    console.log('Available count:', result.availableCount);
    console.log('Registered count:', result.registeredCount);
    console.log('Results array length:', result.results.length);

    if (result.results.length > 0) {
      console.log('Sample result structure:', Object.keys(result.results[0]));
    }

    console.log('✅ Output format test passed');
  } catch (error) {
    console.error('❌ Output format test failed:', error);
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 Starting Domain Search Test Suite');
  console.log('=====================================');

  const startTime = Date.now();

  await testSingleKeywordSearch();
  return
  await testBatchKeywordSearch();
  await testDomainDetails();
  await testErrorHandling();
  await testOutputFormat();

  const endTime = Date.now();
  const duration = (endTime - startTime) / 1000;

  console.log('\n=====================================');
  console.log(`🎉 Test suite completed in ${duration.toFixed(2)} seconds`);
  console.log('=====================================');
}

// 运行测试
if (import.meta.main) {
  runAllTests().catch(console.error);
}

export {
  testSingleKeywordSearch,
  testBatchKeywordSearch,
  testDomainDetails,
  testErrorHandling,
  testOutputFormat,
  runAllTests
};
