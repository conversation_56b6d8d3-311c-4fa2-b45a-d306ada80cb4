/**
 * GitHub Tools for FastMCP
 * 专注于高质量仓库搜索，自动过滤优质项目（>100星，活跃维护）
 * 简化接口，移除过度复杂的参数，内置最佳实践过滤条件
 */

import { z } from "zod";
import { GitHubClient, GitHubSearchParams } from "./client.js";
import { GitHubParser } from "./parser.js";

// ============================================================================
// Tool Definitions - Simple Design
// ============================================================================

// Single search schema - 增强的高质量搜索
const singleSearchSchema = z.object({
  query: z.string().describe(`Search query with support for GitHub Advanced Search syntax.
    Examples:
    - Basic: 'react hooks', 'authentication middleware', 'machine learning python'
    - Exact phrases: '"exact phrase"', '"saas starter"'
    - OR logic: '(saas starter OR saas template OR saas boilerplate)', 'keyword1 OR keyword2'
    - Combined: '"micro saas" OR (saas template AND typescript)'
    - Exclusion: 'react -deprecated', 'auth NOT legacy'
    Use logical operators (AND, OR, NOT) and quotes for exact matching directly in the query, max logic depth is 5`),
  language: z.string().optional().describe("Programming language filter (e.g., 'javascript', 'python', 'typescript')"),
  stars: z.string().optional().describe("Star count filter (e.g., '>100', '100..500')"),
  createdDaysAgo: z.number().optional().describe("Created date filter (e.g., 3, 7, 15, 30, 60, 90, 180, 365)"),
  pushedDaysAgo: z.number().optional().describe("Pushed date filter (e.g., 3, 7, 15, 30, 60, 90, 180, 365)"),
  limit: z.number().min(1).max(50).default(20).describe("Number of results to return (max 50, default 20)"),
  // 新增高级搜索参数
  in: z
    .array(z.enum(["description", "readme", "name", "topics"]))
    .optional()
    .describe("Search scope: description, readme, name, topics. Example: ['description', 'readme']"),
  excludeTerms: z
    .array(z.string())
    .optional()
    .describe("Terms to exclude from search results. Example: ['deprecated', 'archived']")
  // format: z
  //   .enum(["json", "text"])
  //   .default("json")
  //   .describe("Output format: 'json' for structured data, 'text' for formatted text snippets")
});

// Batch search schema
const batchSearchSchema = z.object({
  queries: z
    .array(
      z.object({
        query: z.string().describe(`Search query with support for GitHub Advanced Search syntax.
          Examples:
          - Basic: 'react hooks', 'authentication middleware'
          - Exact phrases: '"exact phrase"', '"saas starter"'
          - OR logic: '(saas starter OR saas template)', 'keyword1 OR keyword2'
          - Combined: '"micro saas" OR (saas template AND typescript)'
          Use logical operators (AND, OR, NOT) and quotes for exact matching directly in the query.`),
        language: z.string().optional().describe("Programming language filter"),
        stars: z.string().optional().describe("Star count filter (e.g., '>100', '100..500')"),
        createdDaysAgo: z.number().optional().describe("Created date filter (e.g., 3, 7, 15, 30, 60, 90, 180, 365)"),
        pushedDaysAgo: z.number().optional().describe("Pushed date filter (e.g., 3, 7, 15, 30, 60, 90, 180, 365)"),
        limit: z.number().min(1).max(50).default(20).optional(),
        // 新增高级搜索参数
        in: z.array(z.enum(["description", "readme", "name", "topics"])).optional(),
        excludeTerms: z.array(z.string()).optional()
      })
    )
    .min(1)
    .max(5)
    .describe("Array of search queries (max 5 for performance)"),
  concurrent: z.boolean().default(true).describe("Whether to run queries concurrently for faster results"),
  deduplicate: z.boolean().default(true).describe("Whether to remove duplicate results across multiple queries")
  // format: z
  //   .enum(["json", "text"])
  //   .default("json")
  //   .optional()
  //   .describe("Output format: 'json' for structured data, 'text' for formatted text snippets")
});

// Output schemas
const searchResultSchema = z.object({
  success: z.boolean().describe("Whether the search was successful"),
  query: z.string().describe("The search query that was used"),
  totalCount: z.number().describe("Total number of results found"),
  resultCount: z.number().describe("Number of results returned"),
  summary: z.string().describe("Summary of search results"),
  content: z.string().describe("Formatted markdown content"),
  repositories: z
    .array(
      z.object({
        id: z.number(),
        name: z.string(),
        fullName: z.string(),
        description: z.string().nullable(),
        url: z.string(),
        language: z.string().nullable(),
        stars: z.number(),
        forks: z.number(),
        topics: z.array(z.string()),
        owner: z.object({
          login: z.string(),
          type: z.string()
        })
      })
    )
    .describe("Repository search results"),
  error: z.string().optional().describe("Error message if search failed")
});

const batchSearchOutputSchema = z.object({
  results: z.array(searchResultSchema).describe("Array of search results"),
  successCount: z.number().describe("Number of successful searches"),
  failureCount: z.number().describe("Number of failed searches")
});

const returnSchema = z.string().describe("Formatted text snippets");

// ============================================================================
// Helper Functions
// ============================================================================

// Deduplication function for batch search results
function deduplicateResults(results: z.infer<typeof searchResultSchema>[]): z.infer<typeof searchResultSchema>[] {
  const seenRepositories = new Set<string>();

  return results.map(result => {
    if (!result.success) return result;

    // Deduplicate repositories by full name
    const uniqueRepos = result.repositories.filter(repo => {
      const key = repo.fullName;
      if (seenRepositories.has(key)) {
        return false;
      }
      seenRepositories.add(key);
      return true;
    });

    return {
      ...result,
      repositories: uniqueRepos,
      resultCount: uniqueRepos.length
    };
  });
}

// Execute single search with enhanced high-quality defaults
async function executeSingleSearch(query: string, options: any = {}): Promise<z.infer<typeof searchResultSchema>> {
  const client = new GitHubClient();
  const parser = new GitHubParser();

  try {
    // 构建增强的高质量搜索参数
    const searchParams: GitHubSearchParams = {
      query,
      searchType: "repositories", // 固定为仓库搜索
      language: options.language,
      createdDaysAgo: options.createdDaysAgo || 365, // 默认1年内创建（相对较新的项目）
      pushedDaysAgo: options.pushedDaysAgo || 365, // 默认1年内有推送（最近有维护）
      per_page: options.limit || 20,
      // 高质量过滤参数
      stars: options.stars || ">100", // 用户指定或默认至少100星
      order: "desc",
      in: options.in,
      excludeTerms: options.excludeTerms
    };

    // 对于仓库搜索，添加额外的质量过滤
    searchParams.is = ["public"]; // 只搜索公开仓库

    const response = await client.search(searchParams);
    const parsed = parser.parseResponse(response, "repositories", options.format);

    return {
      success: true,
      query,
      totalCount: parsed.totalCount,
      resultCount: parsed.repositories?.length || 0,
      summary: parsed.summary,
      content: parsed.markdown,
      repositories: parsed.repositories || []
    };
  } catch (error) {
    console.error(`❌ GitHub search failed for repositories:`, error);

    return {
      success: false,
      query,
      totalCount: 0,
      resultCount: 0,
      summary: "Search failed",
      content: "",
      repositories: [],
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}

// ============================================================================
// Tool Implementations
// ============================================================================

// Single search tool
export const githubSearchTool = {
  name: "github-search",
  description:
    "Enhanced GitHub repository search with advanced syntax support. Features: smart keyword filtering, in:description/readme targeting, exact phrase matching, and quality filtering (>100 stars, actively maintained). Automatically optimizes queries for better results.",
  parameters: singleSearchSchema,
  execute: async (args: z.infer<typeof singleSearchSchema>) => {
    const { query, limit, in: inFields, excludeTerms, ...options } = args;
    const format = "text";
    const result = await executeSingleSearch(query, {
      ...options,
      limit,
      in: inFields,
      excludeTerms,
      format
    });

    return result.content || "No results found";
  }
};

// Batch search tool
export const githubBatchSearchTool = {
  name: "github-batch-search",
  description:
    "Search multiple high-quality GitHub repositories or code patterns concurrently. Maximum 5 queries per batch for efficient comparison.",
  parameters: batchSearchSchema,
  execute: async (args: z.infer<typeof batchSearchSchema>) => {
    const { queries, concurrent, deduplicate } = args;
    const format = "text";

    console.log(`🚀 Starting ${concurrent ? "concurrent" : "sequential"} GitHub batch search for ${queries.length} queries`);

    let results: z.infer<typeof searchResultSchema>[];

    if (concurrent) {
      // Execute all queries concurrently
      results = await Promise.all(
        queries.map(({ query, limit, in: inFields, excludeTerms, ...options }) =>
          executeSingleSearch(query, {
            ...options,
            limit,
            in: inFields,
            excludeTerms,
            format
          })
        )
      );
    } else {
      // Execute queries sequentially
      results = [];
      for (const { query, limit, in: inFields, excludeTerms, ...options } of queries) {
        const result = await executeSingleSearch(query, {
          ...options,
          limit,
          in: inFields,
          excludeTerms,
          format
        });
        results.push(result);
      }
    }

    // Apply deduplication if requested
    if (deduplicate) {
      results = deduplicateResults(results);
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;

    console.log(`✅ GitHub batch search completed: ${successCount} success, ${failureCount} failures`);

    return results.map(r => r.content || "").join("\n");
  }
};
