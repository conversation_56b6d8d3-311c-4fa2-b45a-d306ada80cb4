/**
 * GitHub Search Response Parser
 * Formats and processes GitHub API responses
 */

import { GitHubSearchResponse, GitHubRepository } from "./client.js";

export interface ParsedRepository {
  id: number;
  name: string;
  fullName: string;
  description: string | null;
  url: string;
  cloneUrl: string;
  language: string | null;
  stars: number;
  forks: number;
  size: number;
  createdAt: string;
  updatedAt: string;
  pushedAt: string;
  topics: string[];
  license: string | null;
  owner: {
    login: string;
    type: string;
    avatarUrl: string;
  };
}

export interface ParsedGitHubResponse {
  totalCount: number;
  incompleteResults: boolean;
  repositories?: ParsedRepository[];
  summary: string;
  markdown: string;
}

export class GitHubParser {
  /**
   * Parse GitHub search response
   */
  parseResponse(
    response: GitHubSearchResponse,
    searchType: "repositories",
    format: "json" | "text" = "json"
  ): ParsedGitHubResponse {
    const parsed: ParsedGitHubResponse = {
      totalCount: response.total_count,
      incompleteResults: response.incomplete_results,
      summary: "",
      markdown: ""
    };

    parsed.repositories = this.parseRepositories(response.items);
    parsed.summary = this.generateRepositorySummary(parsed);
    parsed.markdown =
      format === "text"
        ? this.formatRepositoriesAsText(parsed.repositories, parsed.totalCount)
        : this.formatRepositoriesAsMarkdown(parsed.repositories, parsed.totalCount);

    return parsed;
  }

  /**
   * Parse repository items
   */
  private parseRepositories(items: GitHubRepository[]): ParsedRepository[] {
    return items.map(repo => ({
      id: repo.id,
      name: repo.name,
      fullName: repo.full_name,
      description: repo.description,
      url: repo.html_url,
      cloneUrl: repo.clone_url,
      language: repo.language,
      stars: repo.stargazers_count,
      forks: repo.forks_count,
      size: repo.size,
      createdAt: repo.created_at,
      updatedAt: repo.updated_at,
      pushedAt: repo.pushed_at,
      topics: repo.topics || [],
      license: repo.license,
      owner: {
        login: repo.owner.login,
        type: repo.owner.type,
        avatarUrl: repo.owner.avatar_url
      }
    }));
  }

  /**
   * Generate summary for repository search
   */
  private generateRepositorySummary(parsed: ParsedGitHubResponse): string {
    const repos = parsed.repositories || [];
    const languageSet = new Set(repos.map(r => r.language).filter(Boolean));
    const languages = Array.from(languageSet);
    const totalStars = repos.reduce((sum, r) => sum + r.stars, 0);
    const avgStars = repos.length > 0 ? Math.round(totalStars / repos.length) : 0;

    return `Found ${parsed.totalCount} repositories. Top ${repos.length} results include projects in ${languages.slice(0, 3).join(", ")} with an average of ${avgStars} stars.`;
  }

  /**
   * Format repositories as markdown
   */
  private formatRepositoriesAsMarkdown(repositories: ParsedRepository[], totalCount: number): string {
    const lines = [
      `# GitHub Repository Search Results`,
      ``,
      `**Total Results:** ${totalCount}`,
      `**Showing:** ${repositories.length} repositories`,
      ``
    ];

    repositories.forEach((repo, index) => {
      lines.push(`## ${index + 1}. [${repo.fullName}](${repo.url})`);
      lines.push(``);

      if (repo.description) {
        lines.push(`**Description:** ${repo.description}`);
        lines.push(``);
      }

      lines.push(`**Stats:** ⭐ ${repo.stars} | 🍴 ${repo.forks} | 📝 ${repo.language || "N/A"}`);

      if (repo.topics.length > 0) {
        lines.push(`**Topics:** ${repo.topics.map(t => `\`${t}\``).join(", ")}`);
      }

      lines.push(`**Owner:** [@${repo.owner.login}](https://github.com/${repo.owner.login}) (${repo.owner.type})`);
      lines.push(`**Updated:** ${new Date(repo.updatedAt).toLocaleDateString()}`);
      lines.push(``);
      lines.push(`---`);
      lines.push(``);
    });

    return lines.join("\n");
  }

  /**
   * Format repositories as plain text
   */
  private formatRepositoriesAsText(repositories: ParsedRepository[], totalCount: number): string {
    const lines = [
      `GitHub Repository Search Results`,
      `Total Results: ${totalCount}`,
      `Showing: ${repositories.length} repositories`,
      ``
    ];

    repositories.forEach((repo, index) => {
      lines.push(`${index + 1}. ${repo.fullName}`);
      lines.push(`   URL: ${repo.url}`);

      if (repo.description) {
        lines.push(`   Description: ${repo.description}`);
      }

      lines.push(`   Stats: ⭐ ${repo.stars} | 🍴 ${repo.forks} | 📝 ${repo.language || "N/A"}`);

      if (repo.topics.length > 0) {
        lines.push(`   Topics: ${repo.topics.join(", ")}`);
      }

      lines.push(`   Owner: @${repo.owner.login} (${repo.owner.type})`);
      lines.push(`   Updated: ${new Date(repo.updatedAt).toLocaleDateString()}`);
      lines.push(``);
    });

    return lines.join("\n");
  }
}
