#!/usr/bin/env bun
/**
 * RSS HTTP Server
 * Provides RSS feed endpoints for external consumption
 * Refactored with separated route handlers for better maintainability
 */

import {
  parseQueryParams,
  dispatchRoute,
  handleOptions,
  handleMethodNotAllowed,
  handleNotFound
} from "./rss-routes.js";

const PORT = parseInt(process.env.RSS_PORT || "3001");

console.log(`🚀 Starting RSS HTTP Server on port ${PORT}`);

Bun.serve({
  port: PORT,
  fetch: async (req: Request): Promise<Response> => {
    const url = new URL(req.url);
    const pathname = url.pathname;
    const searchParams = url.searchParams;

    // Handle OPTIONS requests for CORS
    if (req.method === "OPTIONS") {
      return handleOptions();
    }

    // Only allow GET requests
    if (req.method !== "GET") {
      return handleMethodNotAllowed();
    }

    try {
      // Parse query parameters
      const options = parseQueryParams(searchParams);

      // Dispatch to appropriate route handler
      const response = await dispatchRoute(pathname, options);

      if (response) {
        return response;
      }

      // 404 for unknown routes
      return handleNotFound();

    } catch (error) {
      console.error("❌ RSS Server Error:", error);

      return new Response(JSON.stringify({
        error: "Internal Server Error",
        message: error instanceof Error ? error.message : "Unknown error"
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    }
  },
});

// All route handlers moved to rss-routes.ts for better organization

console.log(`✅ RSS HTTP Server running at http://localhost:${PORT}`);
console.log(`📡 Simplified RSS endpoints:`);
console.log(`   GET /feed              - Main RSS feed`);
console.log(`   GET /feed/{category}   - Category RSS feed`);
console.log(`   GET /health            - Health check`);
console.log(`📝 Query parameters: ?limit=20&include_content=true&since=2024-01-01`);
console.log(`🎯 Focused on essential RSS 2.0 functionality for maximum compatibility`);
