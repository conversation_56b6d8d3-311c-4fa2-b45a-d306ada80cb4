events {
    worker_connections 1024;
}

http {
    upstream rss_backend {
        server next-agent:3001;
    }

    server {
        listen 80;
        server_name localhost;

        # Proxy RSS feeds
        location /feed {
            proxy_pass http://rss_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Add CORS headers
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, OPTIONS";
            add_header Access-Control-Allow-Headers "Content-Type";
        }

        # Proxy health check
        location /health {
            proxy_pass http://rss_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

        # Simple RSS viewer page
        location / {
            return 200 '
<!DOCTYPE html>
<html>
<head>
    <title>RSS Content Feed</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .feed-link { display: block; margin: 10px 0; padding: 10px; background: #f5f5f5; text-decoration: none; color: #333; border-radius: 5px; }
        .feed-link:hover { background: #e5e5e5; }
        .status { margin: 20px 0; padding: 10px; background: #e8f5e8; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 RSS Content Feed</h1>
        <p>Curated content feed powered by MCP tools</p>
        
        <div class="status" id="status">Checking server status...</div>
        
        <h2>📡 Available RSS Feeds</h2>
        <a href="/feed" class="feed-link">📰 Main RSS Feed</a>
        <a href="/feed/tech" class="feed-link">💻 Tech Category</a>
        <a href="/feed/ai" class="feed-link">🤖 AI Category</a>
        <a href="/feed/web-dev" class="feed-link">🌐 Web Development</a>
        <a href="/feed/devops" class="feed-link">⚙️ DevOps Category</a>
        
        <h2>🔧 API Endpoints</h2>
        <a href="/health" class="feed-link">❤️ Health Check</a>
        
        <script>
            fetch("/health")
                .then(r => r.json())
                .then(data => {
                    document.getElementById("status").innerHTML = 
                        "✅ Server Status: " + data.status + " (Last updated: " + data.timestamp + ")";
                })
                .catch(e => {
                    document.getElementById("status").innerHTML = "❌ Server appears to be offline";
                });
        </script>
    </div>
</body>
</html>';
            add_header Content-Type text/html;
        }
    }
}
