# Bun 配置文件
# 确保使用最新的 Bun 功能和优化

[install]
# 使用最新的包管理器功能
cache = true
exact = false
production = false
optional = true
dev = true
peer = true

[install.scopes]
# 可以在这里配置私有包的 scope

[run]
# 运行时配置
bun = true
shell = "/bin/bash"

[test]
# 测试配置
preload = []
coverage = false

[build]
# 构建配置
target = "node"
format = "esm"
splitting = false
minify = false
sourcemap = "external"

# TypeScript 配置
[typescript]
# 启用最新的 TypeScript 功能
compilerOptions = { strict = true }
