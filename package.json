{"name": "@nxxt/next-agent", "version": "1.0.6", "description": "Next Agent MCP servers for marketing research and repository analysis", "main": "index.js", "bin": {"next-agent": "dist/stdio.js"}, "files": ["dist"], "scripts": {"start": "bun src/sse.ts", "start:mcp": "bun src/sse.ts", "start:rss": "bun src/rss-server.ts", "dev": "bun --watch src/sse.ts", "dev:mcp": "bun --watch src/sse.ts", "dev:rss": "bun --watch src/rss-server.ts", "test:data": "bun src/test-data.ts", "test:markdown": "bun src/test-markdown-content.ts", "test:rss": "bun src/test-data.ts && echo '\\n🧪 Testing RSS endpoints...' && curl -s http://localhost:3001/health"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "engines": {"bun": ">=1.2.3"}, "dependencies": {"@ai-sdk/google": "^1.2.22", "@ai-sdk/openai": "^1.3.23", "@mendable/firecrawl-js": "^1.29.2", "@openrouter/ai-sdk-provider": "^0.7.3", "@types/markdown-it": "^14.1.2", "ai": "^4.3.19", "cheerio": "^1.1.2", "dotenv": "^17.2.1", "fastmcp": "^3.14.1", "feed": "^5.1.0", "form-data": "^4.0.4", "markdown-it": "^14.1.0", "p-retry": "^6.2.1", "undici": "^7.12.0", "zod": "3"}, "devDependencies": {"@types/bun": "^1.2.19", "@types/node": "^24.1.0", "prettier": "^3.6.2", "typescript": "^5.8.3"}}