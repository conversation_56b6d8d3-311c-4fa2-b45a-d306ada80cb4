# Multi-stage Dockerfile for Bun SSE Application
# 使用最新版本的 Bun (Alpine Linux for smaller image size)
FROM oven/bun:latest AS base

# Set working directory
WORKDIR /app

# 显示 Bun 版本信息
RUN echo "Using Bun version:" && bun --version

FROM base AS deps

# Copy package files and Bun config
COPY package.json bun.lockb .bunfig.toml ./

# Install dependencies with cache mount
RUN --mount=type=cache,id=bun,target=/root/.bun/install/cache bun install --frozen-lockfile

FROM base AS production

# Set production environment
ENV NODE_ENV=production

# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules

# Copy application files
COPY package.json bun.lockb .bunfig.toml ./
COPY tsconfig.json ./
COPY src ./src

# Copy environment file (will be overridden by Dokploy env vars)
COPY .env .env

# Expose both MCP and RSS ports
EXPOSE 3000
EXPOSE 3001

# Create startup script to run both servers
RUN echo '#!/bin/sh\nbun src/sse.ts &\nbun src/rss-server.ts &\nwait' > /app/start.sh && chmod +x /app/start.sh

# Start both MCP and RSS servers
CMD ["/app/start.sh"]